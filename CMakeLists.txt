cmake_minimum_required(VERSION 3.16)
project(bladerf_addon)

set(CMAKE_CXX_STANDARD 17)

# Debug configuration for CLion compatibility
set(CMAKE_BUILD_TYPE Debug)
set(CMAKE_CXX_FLAGS_DEBUG "-O0 -g -ggdb3 -fno-omit-frame-pointer -fno-inline-functions -fno-optimize-sibling-calls")
set(CMAKE_C_FLAGS_DEBUG "-O0 -g -ggdb3 -fno-omit-frame-pointer")
set(CMAKE_EXE_LINKER_FLAGS_DEBUG "-g")
set(CMAKE_SHARED_LINKER_FLAGS_DEBUG "-g")

# Ensure debug symbols are generated
set(CMAKE_DEBUG_POSTFIX "")
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Find Node.js
execute_process(COMMAND node -p "process.execPath"
        OUTPUT_VARIABLE NODE_EXECUTABLE
        OUTPUT_STRIP_TRAILING_WHITESPACE)

execute_process(COMMAND node -p "require('path').dirname(process.execPath)"
        OUTPUT_VARIABLE NODE_BIN_DIR
        OUTPUT_STRIP_TRAILING_WHITESPACE)

execute_process(COMMAND node -p "require('path').join(require('path').dirname(process.execPath), '..', 'include', 'node')"
        OUTPUT_VARIABLE NODE_INCLUDE_DIR
        OUTPUT_STRIP_TRAILING_WHITESPACE)

execute_process(COMMAND node -p "require('node-addon-api').include"
        OUTPUT_VARIABLE NODE_ADDON_API_DIR
        OUTPUT_STRIP_TRAILING_WHITESPACE)

# Remove quotes from paths
string(REPLACE "\"" "" NODE_INCLUDE_DIR ${NODE_INCLUDE_DIR})
string(REPLACE "\"" "" NODE_ADDON_API_DIR ${NODE_ADDON_API_DIR})

# Add BladeRF include directories
include_directories(
        ${NODE_INCLUDE_DIR}
        ${NODE_ADDON_API_DIR}
        /usr/local/include
        /usr/include
        /opt/homebrew/include
        /opt/homebrew/Cellar/libbladerf/2023.02_1/include
        # Linux-specific
        /usr/include/libbladeRF
        # Project include directories
        src
        src/chunk-processor
        src/iiq-stream
        src/wav-stream
        src/bladerf-stream
        src/stream-factory
        src/video-converter
        src/video-converter/configs
        src/node
        src/stream-pipeline
        src/video-processor
        src/video-processor/pipeline
        src/signal-processing
        src/signal-processing/find-front-frwd
        src/signal-processing/find-front-frwd-no-ave
        src/signal-processing/sync-by-frwd
)

# Add BladeRF library directories
link_directories(
        /usr/local/lib
        /usr/lib
        /opt/homebrew/lib
        /opt/homebrew/Cellar/libbladerf/2023.02_1/lib
        # Linux-specific
        /usr/lib/aarch64-linux-gnu
        /usr/lib/arm-linux-gnueabihf
)

# Add all your source files
add_library(bladerf_addon SHARED
        src/addon.cpp
        src/wav-stream/wav_stream.cpp
        src/video-processor/video_processor.cpp
        src/video-processor/pipeline/iq_acquisition_node.cpp
        src/video-processor/pipeline/iq_demodulation_node.cpp
        src/video-processor/pipeline/line-detection-node/line_detection_node.cpp
        src/video-processor/pipeline/line-detection-node/partials/segment_ave_filter.cpp
        src/video-processor/pipeline/line-detection-node/partials/video_sync_detector.cpp
        src/video-processor/pipeline/line-detection-node/partials/signal_range_estimator.cpp
        src/video-processor/pipeline/line-detection-node/partials/video_standard_detector.cpp
        src/video-processor/pipeline/line-detection-node/partials/video_sync_state_machine.cpp
        src/video-processor/pipeline/line-detection-node/partials/video_sync_orchestrator.cpp
        src/video-processor/helpers/helpers.cpp
        src/signal-processing/find-front-frwd/find_front_frwd.cpp
        src/signal-processing/find-front-frwd-no-ave/find_front_frwd_no_ave.cpp
        src/signal-processing/sync-by-frwd/sync_by_frwd.cpp
        src/stream-factory/iq_stream_factory.cpp
        src/video-converter/iq_video_stream_processor.cpp
        src/video-converter/processing_helpers.cpp
        src/node/config_helpers.cpp
)

# Link against BladeRF
target_link_libraries(bladerf_addon
        bladeRF
)

# Set compile definitions
target_compile_definitions(bladerf_addon PRIVATE
        NAPI_VERSION=6
        NODE_ADDON_API_DISABLE_DEPRECATED
)

# Platform-specific settings
if(WIN32)
    target_link_libraries(bladerf_addon ${NODE_BIN_DIR}/../node.lib)
    set_target_properties(bladerf_addon PROPERTIES SUFFIX ".node")
elseif(APPLE)
    set_target_properties(bladerf_addon PROPERTIES
            SUFFIX ".node"
            LINK_FLAGS "-undefined dynamic_lookup"
    )
else()
    set_target_properties(bladerf_addon PROPERTIES SUFFIX ".node")
endif()
