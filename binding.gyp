{"targets": [{"target_name": "bladerf_addon", "sources": ["src/addon.cpp", "src/node/config_helpers.cpp", "src/video-processor/video_processor.cpp", "src/video-processor/pipeline/iq_acquisition_node.cpp", "src/video-processor/pipeline/iq_demodulation_node.cpp", "src/video-processor/pipeline/line-detection-node/line_detection_node.cpp", "src/video-processor/pipeline/line-detection-node/partials/segment_ave_filter.cpp", "src/video-processor/pipeline/line-detection-node/partials/segment_pulses_detector.cpp", "src/video-processor/pipeline/line-detection-node/partials/video_sync_detector.cpp", "src/video-processor/pipeline/line-detection-node/partials/signal_range_estimator.cpp", "src/video-processor/pipeline/line-detection-node/partials/video_standard_detector.cpp", "src/video-processor/pipeline/line-detection-node/partials/video_sync_state_machine.cpp", "src/video-processor/pipeline/line-detection-node/partials/video_sync_orchestrator.cpp", "src/video-processor/helpers/helpers.cpp", "src/signal-processing/find-front-frwd/find_front_frwd.cpp", "src/signal-processing/find-front-frwd-no-ave/find_front_frwd_no_ave.cpp", "src/signal-processing/sync-by-frwd/sync_by_frwd.cpp", "src/stream-factory/iq_stream_factory.cpp", "src/wav-stream/wav_stream.cpp", "src/video-converter/iq_video_stream_processor.cpp", "src/video-converter/processing_helpers.cpp"], "include_dirs": ["/usr/local/include", "/usr/include", "/opt/homebrew/include", "/opt/homebrew/Cellar/libbladerf/2023.02_1/include", "src", "src/devtools"], "conditions": [["OS=='mac'", {"xcode_settings": {"GCC_ENABLE_CPP_EXCEPTIONS": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++17", "MACOSX_DEPLOYMENT_TARGET": "10.9", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "STRIP_INSTALLED_PRODUCT": "NO", "DEPLOYMENT_POSTPROCESSING": "NO", "GCC_INLINES_ARE_PRIVATE_EXTERN": "NO", "GCC_SYMBOLS_PRIVATE_EXTERN": "NO"}}], ["OS=='linux'", {"cflags_cc": ["-std=c++17", "-O0", "-g", "-ggdb", "-fno-omit-frame-pointer", "-fno-inline-functions"], "cflags": ["-O0", "-g", "-ggdb", "-fno-omit-frame-pointer"], "conditions": [["target_arch=='arm64'", {"cflags_cc": ["-std=c++17", "-mcpu=cortex-a76", "-mtune=cortex-a76", "-O0", "-g", "-ggdb", "-fno-omit-frame-pointer", "-fno-inline-functions"], "cflags": ["-mcpu=cortex-a76", "-mtune=cortex-a76", "-O0", "-g", "-ggdb", "-fno-omit-frame-pointer"], "ldflags": ["-mcpu=cortex-a76", "-g"]}], ["target_arch=='arm'", {"cflags_cc": ["-std=c++17", "-mcpu=cortex-a53", "-mtune=cortex-a53", "-mfpu=neon-fp-armv8", "-mfloat-abi=hard", "-O0", "-g", "-ggdb", "-fno-omit-frame-pointer", "-fno-inline-functions"], "cflags": ["-mcpu=cortex-a53", "-mtune=cortex-a53", "-mfpu=neon-fp-armv8", "-mfloat-abi=hard", "-O0", "-g", "-ggdb", "-fno-omit-frame-pointer"], "ldflags": ["-mcpu=cortex-a53", "-mfloat-abi=hard", "-g"]}]]}]], "libraries": ["-lbladeRF"], "library_dirs": ["/usr/local/lib", "/usr/lib", "/opt/homebrew/lib", "/opt/homebrew/Cellar/libbladerf/2023.02_1/lib"], "cflags_cc": ["-std=c++17", "-fexceptions", "-Wall", "-Wextra", "-O0", "-g", "-ggdb", "-fno-omit-frame-pointer", "-fno-inline-functions"], "cflags_cc!": ["-fno-exceptions", "-O1", "-O2", "-O3", "-<PERSON><PERSON>", "-Oz"], "cflags": ["-O0", "-g", "-ggdb", "-fno-omit-frame-pointer"], "cflags!": ["-O1", "-O2", "-O3", "-<PERSON><PERSON>", "-Oz"], "configurations": {"Debug": {"defines": ["DEBUG", "_DEBUG"], "cflags_cc": ["-O0", "-g", "-ggdb3", "-fno-omit-frame-pointer", "-fno-inline-functions", "-fno-optimize-sibling-calls"], "cflags": ["-O0", "-g", "-ggdb3", "-fno-omit-frame-pointer"], "ldflags": ["-g"], "xcode_settings": {"GCC_OPTIMIZATION_LEVEL": "0", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "STRIP_INSTALLED_PRODUCT": "NO", "DEPLOYMENT_POSTPROCESSING": "NO"}}, "Release": {"defines": ["NDEBUG"], "cflags_cc": ["-O0", "-g", "-ggdb", "-fno-omit-frame-pointer"], "cflags": ["-O0", "-g", "-ggdb", "-fno-omit-frame-pointer"], "ldflags": ["-g"], "xcode_settings": {"GCC_OPTIMIZATION_LEVEL": "0", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "STRIP_INSTALLED_PRODUCT": "NO", "DEPLOYMENT_POSTPROCESSING": "NO"}}}}]}