# VideoSyncStateMachine Documentation

## Overview

The `VideoSyncStateMachine` class is a state machine designed to process video synchronization pulses and generate events for video frame reconstruction. It analyzes incoming sync pulses to identify different regions of a video signal (horizontal lines, equalizing pulses, vertical sync) and generates appropriate events for downstream processing.

## Purpose

The state machine processes detected video sync pulses and:
- Classifies pulse types (horizontal, equalizing, vertical)
- Tracks video signal regions (horizontal, equalizing, vertical sync)
- Generates events for frame boundaries, line detection, and equalization pulses
- Maintains synchronization with video standards (PAL, NTSC)

## Class Structure

### Header File: video_sync_state_machine.h

```cpp
#pragma once
#include "./video_standard_detector.h"
#include <functional>

namespace IQVideoProcessor::Pipeline {

class VideoSyncStateMachine {
public:
  enum EventType {
    FRAME_BEGIN = 0,
    EQUALIZATION_PULSE = 10,
    LINE_DETECTED = 20,
    EMPTY_LINE = 30,
    FRAME_END = 40,
    UNKNOWN_EVENT = 50
  };

  struct EventData {
    EventType type{UNKNOWN_EVENT};
    size_t frameWidth{0};            // For FRAME_BEGIN events
    size_t frameHeight{0};           // For FRAME_BEGIN events
    uint32_t pulseNumber{0};         // For EQUALIZATION_PULSE events
    uint32_t lineNumber{0};          // For LINE_DETECTED events
    TFloat fromPosition{0};          // Relative data start position
    size_t sampleCount{0};           // Number of samples in this segment
  };

  using EventCallback = std::function<void(const EventData&)>;

  explicit VideoSyncStateMachine(SampleRateType sampleRate);
  void initialize(const VideoStandardDetector::Result& detectedStandard, const EventCallback& eventCallback);
  [[nodiscard]] bool initialized() const;
  void reset();
  void processNextSyncPulse(const VideoSyncPulse& pulse);
  void process(const std::vector<VideoSyncPulse>& videoSyncPulses, double processedSamples);

private:
  enum VideoSyncPulseType {
    UNKNOWN_PULSE = 0,
    HORIZONTAL_SYNC_PULSE = 10,
    EQUALIZING_PULSE = 20,
    HORIZONTAL_OR_EQUALIZING_PULSE = 25,
    VERTICAL_PULSE = 30,
    VERTICAL_LONG_PULSE = 35,
  };

  enum State {
    UNKNOWN_STATE,
    WAITING_FIRST_SYNC_PULSE_STATE,           // Initial state, looking for first sync pulse
    SYNCING_IN_PROGRESS_STATE,                // Sync pulses being classified
    PROCESSING_HORIZONTAL_REGION_STATE,       //
    PROCESSING_EQUALIZING_SYNCH_REGION_STATE, //
    PROCESSING_VERTICAL_SYNC_REGION_STATE,    //
  };

  bool initialized_{false};
  SampleRateType sampleRate_;
  EventCallback eventCallback_;
  VideoStandardDetector::Result ds_{};
  SignalRangeEstimator rangeEstimator_{sampleRate_};

  State previousState_{UNKNOWN_STATE};
  State currentState_{UNKNOWN_STATE};

  VideoSyncPulse previousPulse_{};
  VideoSyncPulseType previousPulseType_{UNKNOWN_PULSE};
  size_t processedLinesCount_{0};
  size_t processedEqPulsesCount_{0};
  size_t processedVSyncPulsesCount_{0};

  inline void setNextState(State nextState);
  inline void setPreviousPulse(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);

  // State handling methods
  inline void handleWaitingFirstSyncPulse(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);
  inline void handleSyncingInProgress(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);
  inline void handleProcessingHorizontalRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);
  inline void handleProcessingEqualizingSyncRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);
  inline void handleProcessingVerticalSyncRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);

  // State entry methods
  inline void enterProcessingHorizontalRegionState(const VideoSyncPulse& pulse);
  inline void enterProcessingEqualizingSyncRegionState(const VideoSyncPulse& pulse);
  inline void enterProcessingVerticalSyncRegionState(const VideoSyncPulse& pulse, bool isFirst);

  // Helpers
  [[nodiscard]] inline VideoSyncPulseType estimatePulseType(const VideoSyncPulse& pulse) const;
  inline void sendEvent(const EventData& eventData);
  inline void sendLineDetectedEvent(uint32_t lineNumber, const VideoSyncPulse& pulse);
  inline void sendEqualizationPulseEvent(uint32_t pulseNumber, const VideoSyncPulse& pulse);
  inline void sendFrameBeginEvent();
  inline void sendFrameEndEvent();
};

}
```

### Implementation File: video_sync_state_machine.cpp

```cpp
#include "./video_sync_state_machine.h"

namespace IQVideoProcessor::Pipeline {
VideoSyncStateMachine::VideoSyncStateMachine(const SampleRateType sampleRate): sampleRate_(sampleRate) {

}

inline void VideoSyncStateMachine::setNextState(const State nextState) {
  if (nextState != currentState_) {
    previousState_ = currentState_;
    currentState_ = nextState;
  }
}

inline void VideoSyncStateMachine::setPreviousPulse(const VideoSyncPulse& pulse, VideoSyncPulseType const pulseType) {
  previousPulse_ = pulse;
  previousPulseType_ = pulseType;
}

inline VideoSyncStateMachine::VideoSyncPulseType VideoSyncStateMachine::estimatePulseType(const VideoSyncPulse& pulse) const {
  switch (rangeEstimator_.estimatePulseByWidth(pulse.width)) {
    case EstimatedPulseType::HORIZONTAL_OR_EQUALIZING_PULSE: return VideoSyncPulseType::HORIZONTAL_OR_EQUALIZING_PULSE;
    case EstimatedPulseType::VERTICAL_PULSE: return VideoSyncPulseType::VERTICAL_PULSE;
    case EstimatedPulseType::VERTICAL_LONG_PULSE: return VideoSyncPulseType::VERTICAL_LONG_PULSE;
    case EstimatedPulseType::UNKNOWN_PULSE:
    default: return VideoSyncPulseType::UNKNOWN_PULSE;
  }
}

[[nodiscard]] bool VideoSyncStateMachine::initialized() const {
  return initialized_;
}

void VideoSyncStateMachine::initialize(const VideoStandardDetector::Result& detectedStandard, const EventCallback& eventCallback) {
  ds_ = detectedStandard;
  eventCallback_ = eventCallback;
  if (ds_.standard == VideoStandard::STANDARD_UNKNOWN) {
    return;
  }
  initialized_ = true;
  setNextState(WAITING_FIRST_SYNC_PULSE_STATE);
}

void VideoSyncStateMachine::reset() {
  previousState_ = UNKNOWN_STATE;
  currentState_ = UNKNOWN_STATE;
  previousPulseType_ = UNKNOWN_PULSE;
  previousPulse_ = {};
  processedLinesCount_ = 0;
  processedEqPulsesCount_ = 0;
  processedVSyncPulsesCount_ = 0;
}

void VideoSyncStateMachine::process(const std::vector<VideoSyncPulse>& videoSyncPulses, const double processingEndPosition) {
  for (const auto& pulse : videoSyncPulses) {
    processNextSyncPulse(pulse);
  }
}

void VideoSyncStateMachine::processNextSyncPulse(const VideoSyncPulse& pulse) {
  const auto pulseType = estimatePulseType(pulse);

  switch (currentState_) {
    case WAITING_FIRST_SYNC_PULSE_STATE: return handleWaitingFirstSyncPulse(pulse, pulseType);
    case SYNCING_IN_PROGRESS_STATE: return handleSyncingInProgress(pulse, pulseType);
    case PROCESSING_HORIZONTAL_REGION_STATE: return handleProcessingHorizontalRegion(pulse, pulseType);
    case PROCESSING_EQUALIZING_SYNCH_REGION_STATE: return handleProcessingEqualizingSyncRegion(pulse, pulseType);
    case PROCESSING_VERTICAL_SYNC_REGION_STATE: return handleProcessingVerticalSyncRegion(pulse, pulseType);
    case UNKNOWN_STATE:
    break;
  }
}

inline void VideoSyncStateMachine::handleWaitingFirstSyncPulse(const VideoSyncPulse& pulse, const VideoSyncPulseType pulseType) {
  if (pulseType == UNKNOWN_PULSE) {
    return;
  }
  setPreviousPulse(pulse, pulseType);
  setNextState(SYNCING_IN_PROGRESS_STATE);
}

inline void VideoSyncStateMachine::handleSyncingInProgress(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {
  if (pulseType == UNKNOWN_PULSE) { // Invalid pulse or distance, lets find another first pulse
    setNextState(WAITING_FIRST_SYNC_PULSE_STATE);
    return;
  }

  const auto distance = pulse.absCenterPosition - previousPulse_.absCenterPosition;
  const auto distanceType = rangeEstimator_.estimateSignalDistance(distance);

  if (distanceType == HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by horizontal distance after any other pulse.
    // It could be horizontal or the first equalizing pulse right after the last horizontal pulse.
    switch (rangeEstimator_.comparePulseByWidth(previousPulse_.width, pulse.width)) {
      case SignalRangeEstimator::LEFT_EQUAL_TO_RIGHT: // Equal widths, so it seems like horizontal
        return enterProcessingHorizontalRegionState(pulse);
      case SignalRangeEstimator::LEFT_GREATER_THAN_RIGHT: // Previous pulse is longer than the current one, consider it equalizing
        return enterProcessingEqualizingSyncRegionState(pulse);
      default: // Not possible, but just in case
        return;
    }
  }
  if (distanceType == HALF_HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by half horizontal distance after any other pulse, consider it equalizing
    return enterProcessingEqualizingSyncRegionState(pulse);
  }
  if (distanceType == HALF_HORIZONTAL_DISTANCE && pulseType == VERTICAL_PULSE) {
    // Having a vertical pulse, followed by half horizontal distance after the previous pulse,
    // consider it vertical somewhere in the middle of ver
    return enterProcessingVerticalSyncRegionState(pulse, previousPulseType_ != VERTICAL_PULSE);
  }
  if (distanceType == _70PERCENT_HORIZONTAL_DISTANCE && pulseType == VERTICAL_PULSE) {
    // Having a vertical pulse, followed by ~70% horizontal distance after the previous pulse,
    // consider it first vertical pulse right after the last equalizing pulse
    return enterProcessingVerticalSyncRegionState(pulse, true);
  }
  if (distanceType == _33PERCENT_HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by ~33% horizontal distance after the previous pulse,
    // consider it first equalizing pulse right after the last vertical pulse
    return enterProcessingEqualizingSyncRegionState(pulse);
  }

  setNextState(WAITING_FIRST_SYNC_PULSE_STATE); // Unable to classify, start over
}

inline void VideoSyncStateMachine::enterProcessingHorizontalRegionState(const VideoSyncPulse& pulse) {
  processedLinesCount_ = 0;
  setPreviousPulse(pulse, VideoSyncPulseType::HORIZONTAL_SYNC_PULSE); // Classify the current pulse as horizontal
  setNextState(PROCESSING_HORIZONTAL_REGION_STATE);
}

inline void VideoSyncStateMachine::enterProcessingEqualizingSyncRegionState(const VideoSyncPulse& pulse) {
  processedEqPulsesCount_ = 1;
  setPreviousPulse(pulse, VideoSyncPulseType::EQUALIZING_PULSE);
  setNextState(PROCESSING_EQUALIZING_SYNCH_REGION_STATE);
}

inline void VideoSyncStateMachine::enterProcessingVerticalSyncRegionState(const VideoSyncPulse& pulse, const bool isFirst) {
  // We can detect if it's a first or a second subsequent pulse only for vertical pulses,
  // because they have deterministic timings in compare to horizontal and equalizing pulses
  processedVSyncPulsesCount_ = isFirst ? 1 : 2;
  setPreviousPulse(pulse, VideoSyncPulseType::VERTICAL_PULSE);
  setNextState(PROCESSING_VERTICAL_SYNC_REGION_STATE);
}

inline void VideoSyncStateMachine::handleProcessingHorizontalRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {
  // Implementation placeholder - currently empty in source
}

inline void VideoSyncStateMachine::handleProcessingEqualizingSyncRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {
  // Implementation placeholder - currently empty in source
}

inline void VideoSyncStateMachine::handleProcessingVerticalSyncRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {
  // Implementation placeholder - currently empty in source
}

// Helper methods for event generation
inline void VideoSyncStateMachine::sendEvent(const EventData& eventData) {
  if (eventCallback_) {
    eventCallback_(eventData);
  }
}

inline void VideoSyncStateMachine::sendLineDetectedEvent(uint32_t lineNumber, const VideoSyncPulse& pulse) {
  EventData event;
  event.type = LINE_DETECTED;
  event.lineNumber = lineNumber;
  sendEvent(event);
}

inline void VideoSyncStateMachine::sendEqualizationPulseEvent(uint32_t pulseNumber, const VideoSyncPulse& pulse) {
  EventData event;
  event.type = EQUALIZATION_PULSE;
  event.pulseNumber = pulseNumber; // Fixed: was incorrectly set to 0 in original code
  sendEvent(event);
}

inline void VideoSyncStateMachine::sendFrameBeginEvent() {
  EventData event;
  event.type = FRAME_BEGIN;
  sendEvent(event);
}

inline void VideoSyncStateMachine::sendFrameEndEvent() {
  EventData event;
  event.type = FRAME_END;
  sendEvent(event);
}

} // namespace IQVideoProcessor::Pipeline
```

## Key Dependencies

### 1. Basic Types

```cpp
// From src/types.h
using SampleType = uint32_t;
using SampleRateType = uint32_t;
using TFloat = float;
```

### 2. Video Standards and Enums

```cpp
// From src/video-processor/types.h
namespace IQVideoProcessor {

enum EstimatedPulseType {
  UNKNOWN_PULSE = 0,
  HORIZONTAL_OR_EQUALIZING_PULSE = 10,
  VERTICAL_PULSE = 20,
  VERTICAL_LONG_PULSE = 30,
};

enum EstimatedDistanceType {
  UNKNOWN_DISTANCE = 0,
  HORIZONTAL_DISTANCE = 10,
  HALF_HORIZONTAL_DISTANCE = 20,
  _33PERCENT_HORIZONTAL_DISTANCE = 30,
  _70PERCENT_HORIZONTAL_DISTANCE = 40,
};

enum VideoStandard {
  STANDARD_UNKNOWN = 0,
  NTSC = 10,
  PAL = 20,
  SECAM = 30,
};

}
```

### 3. VideoSyncPulse Structure

```cpp
// From src/video-processor/pipeline/line-detection-node/partials/video_sync_pulse.h
namespace IQVideoProcessor::Pipeline {

struct VideoSyncPulse: VideoSyncDetector::Result {
  double absFallingFrontPosition{0};
  double absRisingFrontPosition{0};
  double absCenterPosition{0};

  static VideoSyncPulse fromSyncResult(const VideoSyncDetector::Result &r, const double absolutePosition) {
    VideoSyncPulse vsp;
    vsp.fallingFrontPosition = r.fallingFrontPosition;
    vsp.risingFrontPosition = r.risingFrontPosition;
    vsp.centerPosition = r.centerPosition;
    vsp.width = r.width;
    vsp.absFallingFrontPosition = r.fallingFrontPosition + absolutePosition;
    vsp.absRisingFrontPosition = r.risingFrontPosition + absolutePosition;
    vsp.absCenterPosition = r.centerPosition + absolutePosition;
    return vsp;
  };
};

// VideoSyncDetector::Result contains:
struct Result {
  TFloat fallingFrontPosition{0};
  TFloat risingFrontPosition{0};
  TFloat centerPosition{0};
  TFloat width{0};
};

}
```

### 4. VideoStandardDetector::Result Structure

```cpp
// From src/video-processor/pipeline/line-detection-node/partials/video_standard_detector.h
namespace IQVideoProcessor::Pipeline {

class VideoStandardDetector {
public:
  enum DetectionStatus {
    DETECTION_IN_PROGRESS = 0,
    DETECTION_COMPLETE = 10,
    DETECTION_FAILED = 20,
  };

  struct Result {
    VideoStandard standard{STANDARD_UNKNOWN};
    DetectionStatus status{DETECTION_IN_PROGRESS};
    TFloat horizontalFrequencyHz{0};      // ≈ 15625 or ≈ 15734
    TFloat fieldRateHz{0};                // ≈ 50 or ≈ 59.94
    TFloat frameRate{0};                  // FPS = fieldRateHz / 2 (if available)
    TFloat horizontalLineDuration{0};     // samples per line (measured)
    uint32_t linesPerFrame{0};            // 625 or 525

    // Absolute min/max pulse widths (in seconds) across discovered sets
    TFloat hSyncWidthMinSec{0};
    TFloat hSyncWidthMaxSec{0};
    TFloat eqPulseWidthMinSec{0};
    TFloat eqPulseWidthMaxSec{0};

    // Counts around the vertical interval
    uint32_t eqPreCount{0};               // equalizing pulses before the vertical interval (best V run)
    uint32_t eqPostCount{0};              // equalizing pulses after the vertical interval (best V run)
    uint32_t verticalPulseCount{0};       // pulses in the longest vertical run (typically 5 or 6)
  };
};

}
```

### 5. SignalRangeEstimator Class

```cpp
// From src/video-processor/pipeline/line-detection-node/partials/signal_range_estimator.h
namespace IQVideoProcessor::Pipeline {

class SignalRangeEstimator {
public:
  explicit SignalRangeEstimator(SampleRateType sampleRate);

  enum PulseComparison {
    LEFT_LESS_THAN_RIGHT = -1,
    LEFT_EQUAL_TO_RIGHT = 0,
    LEFT_GREATER_THAN_RIGHT = 1,
  };

  [[nodiscard]] EstimatedPulseType estimatePulseByWidth(TFloat pulseWidth) const;
  [[nodiscard]] EstimatedDistanceType estimateSignalDistance(TFloat distance) const;
  [[nodiscard]] PulseComparison comparePulseByWidth(TFloat pulseWidth1, TFloat pulseWidth2) const;

private:
  Range<TFloat> horizontalOrEqualizingPulseWidthRange_{};
  Range<TFloat> verticalSyncPulseWidthRange_{};
  Range<TFloat> verticalSyncLongPulseWidthRange_{};
  Range<TFloat> horizontalLineDistanceRange_{};
  Range<TFloat> horizontalLineHalfDistanceRange_{};
  Range<TFloat> horizontalLine70PercentDistanceRange_{};
  Range<TFloat> horizontalLine33PercentDistanceRange_{};
  SampleRateType sampleRate_;
};

}
```

### 6. Range Utility Template

```cpp
// From src/video-processor/utils/range/range.h
template<typename T>
struct Range {
  T from;
  T to;
  [[nodiscard]] bool inRange(T val) const {
    return val >= from && val <= to;
  }
};
```

### 7. Configuration Constants

```cpp
// From src/video-processor/video_processor_configs.h
// These constants are used by SignalRangeEstimator to define pulse width and distance ranges

// Video sync pulse width constants (in seconds)
constexpr TFloat H_OR_EQ_PULSE_MIN_WIDTH_SEC = 1.5e-6;          // 1.5 microseconds
constexpr TFloat H_OR_EQ_PULSE_MAX_WIDTH_SEC = 7e-6;            // 7 microseconds
constexpr TFloat V_SYNC_PULSE_MIN_WIDTH_SEC = 26e-6;            // 26 microseconds
constexpr TFloat V_SYNC_PULSE_MAX_WIDTH_SEC = 30e-6;            // 30 microseconds
constexpr TFloat V_SYNC_LONG_PULSE_MIN_WIDTH_SEC = 440e-6;      // 440 microseconds
constexpr TFloat V_SYNC_LONG_PULSE_MAX_WIDTH_SEC = 500e-6;      // 500 microseconds

// Horizontal line distance constants (in seconds)
constexpr TFloat H_LINE_DISTANCE_MIN_SEC = 62.3e-6;            // 62.3 microseconds
constexpr TFloat H_LINE_DISTANCE_MAX_SEC = 65.2e-6;            // 65.2 microseconds
constexpr TFloat H_LINE_HALF_DISTANCE_MIN_SEC = 30.0e-6;       // 30.0 microseconds
constexpr TFloat H_LINE_HALF_DISTANCE_MAX_SEC = 35.0e-6;       // 35.0 microseconds
constexpr TFloat H_LINE_70PERCENT_DISTANCE_MIN_SEC = 42.0e-6;  // 42.0 microseconds
constexpr TFloat H_LINE_70PERCENT_DISTANCE_MAX_SEC = 46.0e-6;  // 46.0 microseconds
constexpr TFloat H_LINE_33PERCENT_DISTANCE_MIN_SEC = 18.0e-6;  // 18.0 microseconds
constexpr TFloat H_LINE_33PERCENT_DISTANCE_MAX_SEC = 22.0e-6;  // 22.0 microseconds
```

## Usage Example

Here's how to instantiate and use the VideoSyncStateMachine:

```cpp
#include "video_sync_state_machine.h"
#include <iostream>
#include <vector>

using namespace IQVideoProcessor::Pipeline;

// Event callback function
void onVideoEvent(const VideoSyncStateMachine::EventData& event) {
    switch (event.type) {
        case VideoSyncStateMachine::FRAME_BEGIN:
            std::cout << "Frame Begin - Width: " << event.frameWidth
                      << ", Height: " << event.frameHeight << std::endl;
            break;
        case VideoSyncStateMachine::LINE_DETECTED:
            std::cout << "Line Detected - Line #: " << event.lineNumber
                      << ", Position: " << event.fromPosition << std::endl;
            break;
        case VideoSyncStateMachine::EQUALIZATION_PULSE:
            std::cout << "Equalization Pulse - Pulse #: " << event.pulseNumber << std::endl;
            break;
        case VideoSyncStateMachine::FRAME_END:
            std::cout << "Frame End" << std::endl;
            break;
        default:
            std::cout << "Unknown Event" << std::endl;
            break;
    }
}

int main() {
    // 1. Create the state machine with sample rate
    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoSyncStateMachine stateMachine(sampleRate);

    // 2. Create a detected video standard result (normally from VideoStandardDetector)
    VideoStandardDetector::Result detectedStandard;
    detectedStandard.standard = VideoStandard::PAL;
    detectedStandard.status = VideoStandardDetector::DETECTION_COMPLETE;
    detectedStandard.horizontalFrequencyHz = 15625.0f;
    detectedStandard.fieldRateHz = 50.0f;
    detectedStandard.frameRate = 25.0f;
    detectedStandard.linesPerFrame = 625;

    // 3. Initialize the state machine
    stateMachine.initialize(detectedStandard, onVideoEvent);

    if (!stateMachine.initialized()) {
        std::cerr << "Failed to initialize state machine" << std::endl;
        return -1;
    }

    // 4. Process sync pulses (normally detected from video signal)
    std::vector<VideoSyncPulse> syncPulses;

    // Example sync pulse
    VideoSyncPulse pulse;
    pulse.absCenterPosition = 1000.0;
    pulse.width = 100.0; // Width in samples
    pulse.fallingFrontPosition = 950.0;
    pulse.risingFrontPosition = 1050.0;

    syncPulses.push_back(pulse);

    // 5. Process the pulses
    stateMachine.process(syncPulses, 2000.0);

    // 6. Reset if needed
    stateMachine.reset();

    return 0;
}
```

## Video Standard Detection Examples

The VideoSyncStateMachine relies on VideoStandardDetector::Result for initialization. Here are expected output examples:

### PAL Detection Result
```cpp
VideoStandardDetector::Result palResult = {
    .standard = VideoStandard::PAL,
    .status = VideoStandardDetector::DetectionStatus::DETECTION_COMPLETE,
    .horizontalFrequencyHz = 15624.8789f,
    .fieldRateHz = 49.9996109f,
    .frameRate = 24.9998055f,
    .horizontalLineDuration = 416.003235f,
    .linesPerFrame = 625,
    .hSyncWidthMinSec = 0.00000559443561f,
    .hSyncWidthMaxSec = 0.00000604131628f,
    .eqPulseWidthMinSec = 0.00000271935096f,
    .eqPulseWidthMaxSec = 0.00000596108794f,
    .eqPreCount = 5,
    .eqPostCount = 5,
    .verticalPulseCount = 5
};
```

### NTSC Detection Result
```cpp
VideoStandardDetector::Result ntscResult = {
    .standard = VideoStandard::NTSC,
    .status = VideoStandardDetector::DetectionStatus::DETECTION_COMPLETE,
    .horizontalFrequencyHz = 15734.4951f,
    .fieldRateHz = 59.9410057f,
    .frameRate = 29.9705029f,
    .horizontalLineDuration = 444.882416f,
    .linesPerFrame = 525,
    .hSyncWidthMinSec = 0.00000351363701f,
    .hSyncWidthMaxSec = 0.00000461132822f,
    .eqPulseWidthMinSec = 0.00000198297994f,
    .eqPulseWidthMaxSec = 0.00000452873883f,
    .eqPreCount = 6,
    .eqPostCount = 7,
    .verticalPulseCount = 6
};
```

## State Machine Behavior

### State Transitions

1. **UNKNOWN_STATE** → **WAITING_FIRST_SYNC_PULSE_STATE**: After initialization
2. **WAITING_FIRST_SYNC_PULSE_STATE** → **SYNCING_IN_PROGRESS_STATE**: When first valid pulse is detected
3. **SYNCING_IN_PROGRESS_STATE** → **PROCESSING_HORIZONTAL_REGION_STATE**: When horizontal sync pattern is identified
4. **SYNCING_IN_PROGRESS_STATE** → **PROCESSING_EQUALIZING_SYNCH_REGION_STATE**: When equalizing pulse pattern is identified
5. **SYNCING_IN_PROGRESS_STATE** → **PROCESSING_VERTICAL_SYNC_REGION_STATE**: When vertical sync pattern is identified

### Pulse Classification Logic

The state machine uses the SignalRangeEstimator to classify pulses based on:

- **Pulse Width**: Determines if pulse is horizontal/equalizing, vertical, or vertical long
- **Pulse Distance**: Measures time between consecutive pulses to identify sync patterns
- **Pulse Comparison**: Compares pulse widths to distinguish between horizontal and equalizing pulses

### Event Generation

The state machine generates events through the callback mechanism:

- **FRAME_BEGIN**: Marks the start of a new video frame
- **LINE_DETECTED**: Indicates detection of a horizontal line
- **EQUALIZATION_PULSE**: Marks equalizing pulses in the vertical interval
- **FRAME_END**: Marks the end of a video frame
- **EMPTY_LINE**: Indicates missing or invalid line data

## Key Features

### Robust Sync Detection
- Handles noisy or imperfect sync signals
- Recovers from sync loss by returning to initial state
- Uses multiple criteria for pulse classification

### Video Standard Awareness
- Adapts behavior based on detected video standard (PAL/NTSC)
- Uses standard-specific timing parameters
- Supports different pulse counts and timing for each standard

### Event-Driven Architecture
- Provides callback-based event notification
- Includes detailed event data for downstream processing
- Supports real-time video processing pipelines

## Implementation Notes

### Current Limitations
- Some state handlers are not fully implemented (marked as placeholders)
- The state machine focuses on sync detection rather than full frame reconstruction
- Event data fields like `frameWidth`, `frameHeight`, and `sampleCount` may need additional implementation

### Thread Safety
- The class is not inherently thread-safe
- External synchronization required for multi-threaded usage
- Event callbacks should be thread-safe if used in multi-threaded context

### Performance Considerations
- Uses inline functions for performance-critical operations
- Minimal memory allocation during processing
- Efficient state transition logic

## Integration Requirements

To use VideoSyncStateMachine in your project, ensure you have:

1. **VideoStandardDetector**: For initial video standard detection
2. **SignalRangeEstimator**: For pulse classification
3. **VideoSyncPulse**: Input data structure
4. **Range utility**: Template for range checking
5. **Basic types**: SampleRateType, TFloat definitions

The state machine is designed to work as part of a larger video processing pipeline, typically receiving sync pulses from a VideoSyncDetector and generating events for downstream video reconstruction components.
