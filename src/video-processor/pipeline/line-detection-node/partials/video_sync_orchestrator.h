#pragma once

#include "./video_standard_detector.h"
#include "./video_sync_pulse.h"
#include "./signal_range_estimator.h"

#include "../../../types.h"
#include "../../../../types.h"

#include <functional>
#include <vector>
#include <cstdint>
#include <cstddef>
#include <cmath>

namespace IQVideoProcessor::Pipeline {

// Extremely simple, prediction-first "wheel" orchestrator.
// - Build a fixed wheel of steps (H, EQ, V) from the detected standard.
// - Walk the wheel by adding a known distance per step (H or H/2).
// - Emit events (FRAME_BEGIN/END, LINE_DETECTED, EQUALIZATION_PULSE).
// - Use real pulses only to phase-correct when they land near the expected center.
// - First lock: seek a VERTICAL pulse in the input pulses; until then, do nothing.

class VideoSyncOrchestrator {
public:
  enum EventType {
    FRAME_BEGIN = 0,
    EQUALIZATION_PULSE = 10,
    LINE_DETECTED = 20,
    EMPTY_LINE = 30,
    FRAME_END = 40,
    UNKNOWN_EVENT = 50
  };

  enum VideoSyncPulseType {
    UNKNOWN_PULSE = 0,
    HORIZONTAL_SYNC_PULSE = 10,
    EQUALIZING_PULSE = 20,
    HORIZONTAL_OR_EQUALIZING_PULSE = 25,
    VERTICAL_PULSE = 30,
    VERTICAL_LONG_PULSE = 35,
  };

  struct EventData {
    EventType type{UNKNOWN_EVENT};
    size_t frameWidth{720};      // per Q/A: constant for now
    size_t frameHeight{0};       // ds_.linesPerFrame
    uint32_t pulseNumber{0};     // for EQUALIZATION_PULSE (monotonic across run)
    uint32_t lineNumber{0};      // for LINE_DETECTED (starts at 0 per frame)
    TFloat fromPosition{0};      // absolute position (center-to-center start)
    size_t sampleCount{0};       // center-to-center length
  };

  using EventCallback = std::function<void(const EventData&)>;

  // Optional preset – if not AUTO_FROM_DS, a canonical wheel is built for the chosen profile.
  enum class WheelProfile {
    AUTO_FROM_DS = 0,
    NTSC_525_60,
    PAL_625_50,
    PAL_625_50_INTERLACED // alias of PAL_625_50 (provided for clarity)
  };

  explicit VideoSyncOrchestrator(SampleRateType sampleRate);

  // Configure canonical wheel profile (optional). Call before initialize().
  void setWheelProfile(WheelProfile profile);

  // Initialize with detected standard (we trust ds_.horizontalLineDuration, linesPerFrame, etc.).
  void initialize(const VideoStandardDetector::Result& detectedStandard,
                  const EventCallback& cb);

  [[nodiscard]] bool initialized() const;
  void reset();

  // Process one segment: all detected pulses and absolute processing end position.
  // Pulses vector must be sorted by absCenterPosition ascending (as per your pipeline).
  void process(const std::vector<VideoSyncPulse>& pulses, double processingEndPosition);

private:
  // Wheel representation
  enum class StepType { H, EQ, V };
  struct WheelStep {
    StepType type{StepType::H};
    double distance{0.0}; // in samples (H or H/2)
  };

  // Internals
  void clearRuntime();
  void computeTolerance();
  void buildWheel(); // uses either ds_ or preset profile/hardcoded counts
  void buildWheelFromDS(uint32_t linesPerFrame,
                        uint32_t eqPre,
                        uint32_t eqPost,
                        uint32_t vCount);
  void buildWheelPresetNTSC();
  void buildWheelPresetPAL();
  void buildWheelPresetPALInterlaced(); // same as PAL, explicit alias

  // First-time anchor: find first vertical pulse in this segment
  bool tryInitialLockOnVertical(const std::vector<VideoSyncPulse>& pulses);

  // Phase correction and pulse matching
  bool findMatchingPulse(const std::vector<VideoSyncPulse>& pulses,
                        double expectedPosition,
                        StepType expectedType,
                        double& actualPosition) const;



  // Events
  void sendEvent(const EventData& e);
  void sendFrameBegin();
  void sendFrameEnd();
  void sendLineDetected(double fromCenter, double toCenter);
  void sendEqPulse(double fromCenter, double toCenter);

  // Helpers
  inline VideoSyncPulseType estimatePulseType(const VideoSyncPulse& p) const;

private:
  // Config
  SampleRateType sampleRate_{0};
  EventCallback callback_{};
  VideoStandardDetector::Result detectedStandard_{};
  WheelProfile profile_{WheelProfile::AUTO_FROM_DS};
  SignalRangeEstimator rangeEstimator_;

  // Tolerances and timing
  double horizontalLineDuration_{0.0};         // samples per horizontal line (center-to-center)
  double halfHorizontalLineDuration_{0.0};     // H/2 (equalizing / vertical cadence)
  double toleranceSamples_{0.0}; // ±3us in samples, clamped to [2 samples, 0.15*H]

  // Wheel
  std::vector<WheelStep> wheel_; // 2-frame cycle, starting with vertical entry
  size_t wheelIdx_{0};           // index of the last completed step (so next is wheelIdx_+1 % wheel_.size())

  // Runtime state
  bool inited_{false};
  bool locked_{false};            // true after initial vertical pulse found
  bool frameActive_{false};       // inside frame (between leaving V and entering V)
  uint32_t lineInFrame_{0};
  uint32_t eqPulseCounter_{0};    // monotonic counter (can be reset on FRAME_BEGIN if desired)
  double currentCenter_{0.0};     // absolute center of the last completed step end

  // Statistics and debugging
  uint32_t totalPhaseCorrectionCount_{0};
  uint32_t totalProcessedSteps_{0};
};

} // namespace IQVideoProcessor::Pipeline
