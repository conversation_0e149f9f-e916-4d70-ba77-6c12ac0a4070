#include "./video_sync_pulse_predictor.h"
#include "../../../video_processor_configs.h"

namespace IQVideoProcessor {

VideoSyncPulsePredictor::VideoSyncPulsePredictor(const Config& config)
  : config_(config), signalRangeEstimator_(config.sampleRate) {
  initializeFromStandard();
}

void VideoSyncPulsePredictor::initializeFromStandard() {
  const auto& result = config_.standardResult;
  
  // Extract timing parameters from standard detection result
  horizontalLineDuration_ = result.horizontalLineDuration;
  halfLineDuration_ = horizontalLineDuration_ / static_cast<TFloat>(2.0);
  
  // Set video standard specific parameters
  if (result.standard == PAL) {
    expectedVerticalPulses_ = 5;
    expectedPreEqPulses_ = result.eqPreCount > 0 ? result.eqPreCount : 5;
    expectedPostEqPulses_ = result.eqPostCount > 0 ? result.eqPostCount : 5;
    expectedLinesPerFrame_ = 625;
  } else if (result.standard == NTSC) {
    expectedVerticalPulses_ = 6;
    expectedPreEqPulses_ = result.eqPreCount > 0 ? result.eqPreCount : 6;
    expectedPostEqPulses_ = result.eqPostCount > 0 ? result.eqPostCount : 6;
    expectedLinesPerFrame_ = 525;
  } else {
    // Unknown standard - use detected values or defaults
    expectedVerticalPulses_ = result.verticalPulseCount > 0 ? result.verticalPulseCount : 5;
    expectedPreEqPulses_ = result.eqPreCount > 0 ? result.eqPreCount : 5;
    expectedPostEqPulses_ = result.eqPostCount > 0 ? result.eqPostCount : 5;
    expectedLinesPerFrame_ = result.linesPerFrame > 0 ? result.linesPerFrame : 625;
  }
  
  // Initialize state
  currentState_ = SEEKING_VERTICAL_SYNC;
  currentFrameState_ = FRAME_UNKNOWN;
  currentFrameNumber_ = 0;
  currentLineNumber_ = 0;
  frameInProgress_ = false;
}

bool VideoSyncPulsePredictor::processPulses(const std::vector<VideoSyncPulse>& detectedPulses) {
  if (detectedPulses.empty()) {
    return true; // Nothing to process
  }
  
  for (currentPulseIndex_ = 0; currentPulseIndex_ < detectedPulses.size(); ++currentPulseIndex_) {
    const auto& pulse = detectedPulses[currentPulseIndex_];
    
    switch (currentState_) {
      case SEEKING_VERTICAL_SYNC:
        if (!handleVerticalSyncDetection(pulse)) {
          currentState_ = PREDICTION_ERROR;
          return false;
        }
        break;
        
      case IN_VERTICAL_INTERVAL:
        if (!processVerticalInterval(pulse)) {
          currentState_ = PREDICTION_ERROR;
          return false;
        }
        break;
        
      case IN_HORIZONTAL_LINES:
        if (!processHorizontalLine(pulse)) {
          currentState_ = PREDICTION_ERROR;
          return false;
        }
        break;
        
      case PREDICTION_ERROR:
      case UNINITIALIZED:
        return false;
    }
    
    lastPulsePosition_ = pulse.absCenterPosition;
  }
  
  return true;
}

EstimatedPulseType VideoSyncPulsePredictor::predictNextPulseType() const {
  switch (currentFrameState_) {
    case PRE_EQUALIZING:
      return HORIZONTAL_OR_EQUALIZING_PULSE;
      
    case VERTICAL_SYNC:
      return VERTICAL_PULSE;
      
    case POST_EQUALIZING:
      return HORIZONTAL_OR_EQUALIZING_PULSE;
      
    case HORIZONTAL_LINES:
      return HORIZONTAL_OR_EQUALIZING_PULSE;
      
    case FRAME_UNKNOWN:
    default:
      return UNKNOWN_PULSE;
  }
}

EstimatedPulseType VideoSyncPulsePredictor::classifyPulse(const VideoSyncPulse& pulse) const {
  return signalRangeEstimator_.estimatePulseByWidth(pulse.width);
}

bool VideoSyncPulsePredictor::validatePulse(const VideoSyncPulse& pulse, EstimatedPulseType predicted) const {
  const EstimatedPulseType classified = classifyPulse(pulse);
  
  // Check if classified type matches prediction
  if (predicted == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Both horizontal and equalizing pulses have same width range
    return classified == HORIZONTAL_OR_EQUALIZING_PULSE;
  }
  
  return classified == predicted;
}

bool VideoSyncPulsePredictor::validateTiming(const VideoSyncPulse& pulse) const {
  if (lastPulsePosition_ == 0) {
    return true; // First pulse, no timing to validate
  }
  
  const TFloat actualDistance = pulse.absCenterPosition - lastPulsePosition_;
  const EstimatedDistanceType distanceType = signalRangeEstimator_.estimateSignalDistance(actualDistance);
  
  switch (currentFrameState_) {
    case PRE_EQUALIZING:
    case POST_EQUALIZING:
    case VERTICAL_SYNC:
      // Expect half-line distances in vertical interval
      return distanceType == HALF_HORIZONTAL_DISTANCE || 
             distanceType == _33PERCENT_HORIZONTAL_DISTANCE ||
             distanceType == _70PERCENT_HORIZONTAL_DISTANCE;
      
    case HORIZONTAL_LINES:
      // Expect full horizontal line distances
      return distanceType == HORIZONTAL_DISTANCE;
      
    case FRAME_UNKNOWN:
    default:
      return true; // Accept any timing when state is unknown
  }
}

TFloat VideoSyncPulsePredictor::calculateExpectedPosition(EstimatedPulseType pulseType) const {
  if (lastPulsePosition_ == 0) {
    return 0; // No reference position
  }
  
  switch (currentFrameState_) {
    case PRE_EQUALIZING:
    case POST_EQUALIZING:
    case VERTICAL_SYNC:
      // Half-line spacing in vertical interval
      return lastPulsePosition_ + halfLineDuration_;
      
    case HORIZONTAL_LINES:
      // Full line spacing for horizontal lines
      return lastPulsePosition_ + horizontalLineDuration_;
      
    case FRAME_UNKNOWN:
    default:
      return lastPulsePosition_ + horizontalLineDuration_;
  }
}

VideoSyncPulse VideoSyncPulsePredictor::synthesizeMissingPulse(EstimatedPulseType expectedType) const {
  VideoSyncPulse synthesized;
  
  // Calculate expected position
  synthesized.absCenterPosition = calculateExpectedPosition(expectedType);
  synthesized.absRisingFrontPosition = synthesized.absCenterPosition;
  synthesized.absFallingFrontPosition = synthesized.absCenterPosition;
  
  // Set appropriate width based on pulse type
  switch (expectedType) {
    case HORIZONTAL_OR_EQUALIZING_PULSE:
      synthesized.width = static_cast<TFloat>(config_.sampleRate) * static_cast<TFloat>(H_OR_EQ_PULSE_MIN_WIDTH_SEC);
      break;
      
    case VERTICAL_PULSE:
      synthesized.width = static_cast<TFloat>(config_.sampleRate) * static_cast<TFloat>(V_SYNC_PULSE_MIN_WIDTH_SEC);
      break;
      
    case VERTICAL_LONG_PULSE:
      synthesized.width = static_cast<TFloat>(config_.sampleRate) * static_cast<TFloat>(V_SYNC_LONG_PULSE_MIN_WIDTH_SEC);
      break;
      
    default:
      synthesized.width = static_cast<TFloat>(config_.sampleRate) * static_cast<TFloat>(H_OR_EQ_PULSE_MIN_WIDTH_SEC);
      break;
  }
  
  return synthesized;
}

bool VideoSyncPulsePredictor::shouldSynthesizeMissingPulse(const VideoSyncPulse& nextPulse) const {
  if (lastPulsePosition_ == 0) {
    return false; // No reference for synthesis
  }
  
  const TFloat expectedPosition = calculateExpectedPosition(predictNextPulseType());
  const TFloat actualDistance = nextPulse.absCenterPosition - lastPulsePosition_;
  const TFloat expectedDistance = expectedPosition - lastPulsePosition_;
  
  // If actual distance is significantly larger than expected, we might have missed a pulse
  return actualDistance > (expectedDistance * static_cast<TFloat>(1.5));
}

void VideoSyncPulsePredictor::sendEvent(const EventData& eventData) {
  if (config_.callback) {
    config_.callback(eventData);
  }
}

bool VideoSyncPulsePredictor::handleVerticalSyncDetection(const VideoSyncPulse& pulse) {
  const EstimatedPulseType classified = classifyPulse(pulse);

  // Look for vertical sync pulse to establish synchronization
  if (classified == VERTICAL_PULSE || classified == VERTICAL_LONG_PULSE) {
    // Found vertical sync - initialize frame processing
    currentState_ = IN_VERTICAL_INTERVAL;
    currentFrameState_ = VERTICAL_SYNC;
    verticalPulseCount_ = 1;
    equalizationPulseCount_ = 0;

    if (!frameInProgress_) {
      // Start new frame
      frameInProgress_ = true;
      currentFrameNumber_++;
      currentLineNumber_ = 0;

      EventData frameBeginEvent;
      frameBeginEvent.type = FRAME_BEGIN;
      frameBeginEvent.frameWidth = expectedLinesPerFrame_;
      frameBeginEvent.frameHeight = expectedLinesPerFrame_;
      frameBeginEvent.dataStartPosition = pulse.absCenterPosition;
      frameBeginEvent.sampleCount = 0;
      sendEvent(frameBeginEvent);
    }

    return true;
  }

  // Continue seeking if not found
  return true;
}

bool VideoSyncPulsePredictor::processVerticalInterval(const VideoSyncPulse& pulse) {
  const EstimatedPulseType predicted = predictNextPulseType();
  const EstimatedPulseType classified = classifyPulse(pulse);

  // Validate timing
  if (!validateTiming(pulse)) {
    return false;
  }

  // Check for missing pulses
  if (shouldSynthesizeMissingPulse(pulse)) {
    // Synthesize missing pulse and process it
    VideoSyncPulse missingPulse = synthesizeMissingPulse(predicted);
    updatePredictionState(missingPulse, predicted);
  }

  // Process current pulse
  updatePredictionState(pulse, classified);

  return true;
}

bool VideoSyncPulsePredictor::processHorizontalLine(const VideoSyncPulse& pulse) {
  const EstimatedPulseType predicted = predictNextPulseType();
  const EstimatedPulseType classified = classifyPulse(pulse);

  // Validate timing
  if (!validateTiming(pulse)) {
    return false;
  }

  // Check for missing pulses
  if (shouldSynthesizeMissingPulse(pulse)) {
    // Synthesize missing pulse and process it
    VideoSyncPulse missingPulse = synthesizeMissingPulse(predicted);
    updatePredictionState(missingPulse, predicted);
  }

  // Process current pulse
  updatePredictionState(pulse, classified);

  // Check if we're approaching next vertical interval
  if (currentLineNumber_ >= (expectedLinesPerFrame_ - expectedPreEqPulses_ - expectedVerticalPulses_ - expectedPostEqPulses_)) {
    // Look for transition to equalizing pulses
    const TFloat nextDistance = calculateExpectedPosition(HORIZONTAL_OR_EQUALIZING_PULSE) - pulse.absCenterPosition;
    if (signalRangeEstimator_.isHalfHorizontalLineDistance(nextDistance)) {
      currentFrameState_ = PRE_EQUALIZING;
      equalizationPulseCount_ = 0;
    }
  }

  return true;
}

void VideoSyncPulsePredictor::updatePredictionState(const VideoSyncPulse& pulse, EstimatedPulseType pulseType) {
  switch (currentFrameState_) {
    case PRE_EQUALIZING:
      if (pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
        equalizationPulseCount_++;

        EventData eqEvent;
        eqEvent.type = EQUALIZATION_PULSE;
        eqEvent.pulseNumber = equalizationPulseCount_;
        eqEvent.dataStartPosition = pulse.absCenterPosition;
        eqEvent.sampleCount = static_cast<size_t>(halfLineDuration_);
        sendEvent(eqEvent);

        if (equalizationPulseCount_ >= expectedPreEqPulses_) {
          advanceFrameState();
        }
      }
      break;

    case VERTICAL_SYNC:
      if (pulseType == VERTICAL_PULSE || pulseType == VERTICAL_LONG_PULSE) {
        verticalPulseCount_++;
        if (verticalPulseCount_ >= expectedVerticalPulses_) {
          advanceFrameState();
        }
      }
      break;

    case POST_EQUALIZING:
      if (pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
        equalizationPulseCount_++;

        EventData eqEvent;
        eqEvent.type = EQUALIZATION_PULSE;
        eqEvent.pulseNumber = expectedPreEqPulses_ + expectedVerticalPulses_ + equalizationPulseCount_;
        eqEvent.dataStartPosition = pulse.absCenterPosition;
        eqEvent.sampleCount = static_cast<size_t>(halfLineDuration_);
        sendEvent(eqEvent);

        if (equalizationPulseCount_ >= expectedPostEqPulses_) {
          advanceFrameState();
        }
      }
      break;

    case HORIZONTAL_LINES:
      if (pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
        currentLineNumber_++;

        EventData lineEvent;
        lineEvent.type = LINE_DETECTED;
        lineEvent.lineNumber = currentLineNumber_;
        lineEvent.dataStartPosition = pulse.absCenterPosition;
        lineEvent.sampleCount = static_cast<size_t>(horizontalLineDuration_);
        sendEvent(lineEvent);

        // Check for frame completion
        if (currentLineNumber_ >= expectedLinesPerFrame_) {
          EventData frameEndEvent;
          frameEndEvent.type = FRAME_END;
          frameEndEvent.dataStartPosition = pulse.absCenterPosition;
          frameEndEvent.sampleCount = 0;
          sendEvent(frameEndEvent);

          frameInProgress_ = false;
          currentState_ = SEEKING_VERTICAL_SYNC;
          currentFrameState_ = FRAME_UNKNOWN;
        }
      }
      break;

    case FRAME_UNKNOWN:
    default:
      break;
  }
}

void VideoSyncPulsePredictor::advanceFrameState() {
  switch (currentFrameState_) {
    case PRE_EQUALIZING:
      currentFrameState_ = VERTICAL_SYNC;
      verticalPulseCount_ = 0;
      break;

    case VERTICAL_SYNC:
      currentFrameState_ = POST_EQUALIZING;
      equalizationPulseCount_ = 0;
      break;

    case POST_EQUALIZING:
      currentFrameState_ = HORIZONTAL_LINES;
      currentState_ = IN_HORIZONTAL_LINES;
      currentLineNumber_ = 0;
      break;

    case HORIZONTAL_LINES:
      // Should not advance from here - handled in updatePredictionState
      break;

    case FRAME_UNKNOWN:
    default:
      break;
  }
}

void VideoSyncPulsePredictor::reset() {
  currentState_ = SEEKING_VERTICAL_SYNC;
  currentFrameState_ = FRAME_UNKNOWN;
  currentFrameNumber_ = 0;
  currentLineNumber_ = 0;
  equalizationPulseCount_ = 0;
  verticalPulseCount_ = 0;
  lastPulsePosition_ = 0;
  expectedNextPulsePosition_ = 0;
  frameInProgress_ = false;
}

} // namespace IQVideoProcessor
