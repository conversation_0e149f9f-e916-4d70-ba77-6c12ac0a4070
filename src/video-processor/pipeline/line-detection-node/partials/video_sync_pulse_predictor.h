#pragma once

#include "../../../types.h"
#include "../../../../types.h"
#include "./signal_range_estimator.h"
#include "./video_standard_detector.h"
#include "./video_sync_pulse.h"

#include <vector>
#include <functional>

namespace IQVideoProcessor {

/**
 * VideoSyncPulsePredictor - Comprehensive video sync pulse prediction and validation system
 * 
 * This class implements a state machine that predicts expected sync pulse types based on
 * video standard timing (PAL/NTSC), validates detected pulses against predictions,
 * handles missing pulse synthesis, and provides structured callbacks for frame/line events.
 */
class VideoSyncPulsePredictor {
public:
  /**
   * Video sync prediction state machine states
   */
  enum PredictionState {
    UNINITIALIZED = 0,
    SEEKING_VERTICAL_SYNC = 10,        // Looking for first vertical sync to establish reference
    IN_VERTICAL_INTERVAL = 20,         // Processing vertical sync interval (EQ + V + EQ)
    IN_HORIZONTAL_LINES = 30,          // Processing horizontal sync lines
    PREDICTION_ERROR = 40              // Prediction failed, needs resynchronization
  };

  /**
   * Video frame structure states for PAL/NTSC
   */
  enum FrameState {
    FRAME_UNKNOWN = 0,
    PRE_EQUALIZING = 10,               // Equalizing pulses before vertical sync
    VERTICAL_SYNC = 20,                // Vertical sync pulses
    POST_EQUALIZING = 30,              // Equalizing pulses after vertical sync
    HORIZONTAL_LINES = 40              // Regular horizontal sync lines
  };

  /**
   * Event types for callback notifications
   */
  enum EventType {
    FRAME_BEGIN = 0,
    EQUALIZATION_PULSE = 10,
    LINE_DETECTED = 20,
    EMPTY_LINE = 30,
    FRAME_END = 40
  };

  /**
   * Event data structure passed to callbacks
   */
  struct EventData {
    EventType type;
    uint32_t frameWidth{0};            // For FRAME_BEGIN events
    uint32_t frameHeight{0};           // For FRAME_BEGIN events
    uint32_t pulseNumber{0};           // For EQUALIZATION_PULSE events
    uint32_t lineNumber{0};            // For LINE_DETECTED events
    TFloat dataStartPosition{0};       // Relative data start position
    size_t sampleCount{0};             // Number of samples in this segment
  };

  /**
   * Callback function type for event notifications
   * Uses reference parameter as per user preferences
   */
  using EventCallback = std::function<void(const EventData&)>;

  /**
   * Configuration structure for predictor initialization
   */
  struct Config {
    SampleRateType sampleRate;
    VideoStandardDetector::Result standardResult;
    EventCallback callback;
  };

  /**
   * Constructor
   */
  explicit VideoSyncPulsePredictor(const Config& config);

  /**
   * Process detected sync pulses and generate predictions/callbacks
   * 
   * @param detectedPulses Array of detected sync pulses to process
   * @return true if processing successful, false if prediction error occurred
   */
  bool processPulses(const std::vector<VideoSyncPulse>& detectedPulses);

  /**
   * Reset predictor state (useful after prediction errors)
   */
  void reset();

  /**
   * Get current prediction state
   */
  [[nodiscard]] PredictionState getCurrentState() const { return currentState_; }

  /**
   * Get current frame state
   */
  [[nodiscard]] FrameState getCurrentFrameState() const { return currentFrameState_; }

private:
  // Configuration
  Config config_;
  SignalRangeEstimator signalRangeEstimator_;

  // State tracking
  PredictionState currentState_{UNINITIALIZED};
  FrameState currentFrameState_{FRAME_UNKNOWN};
  
  // Frame/line counters
  uint32_t currentFrameNumber_{0};
  uint32_t currentLineNumber_{0};
  uint32_t equalizationPulseCount_{0};
  uint32_t verticalPulseCount_{0};
  
  // Timing predictions
  TFloat lastPulsePosition_{0};
  TFloat expectedNextPulsePosition_{0};
  TFloat horizontalLineDuration_{0};     // Samples per horizontal line
  TFloat halfLineDuration_{0};           // Half line duration for EQ/V pulses
  
  // Video standard parameters
  uint32_t expectedVerticalPulses_{0};   // Expected number of vertical pulses (5 for PAL, 6 for NTSC)
  uint32_t expectedPreEqPulses_{0};      // Expected pre-equalizing pulses
  uint32_t expectedPostEqPulses_{0};     // Expected post-equalizing pulses
  uint32_t expectedLinesPerFrame_{0};    // Total lines per frame
  
  // Pulse processing state
  size_t currentPulseIndex_{0};
  bool frameInProgress_{false};

  /**
   * Initialize predictor with video standard parameters
   */
  void initializeFromStandard();

  /**
   * Predict the next expected pulse type based on current state
   */
  [[nodiscard]] EstimatedPulseType predictNextPulseType() const;

  /**
   * Classify a detected pulse using SignalRangeEstimator
   */
  [[nodiscard]] EstimatedPulseType classifyPulse(const VideoSyncPulse& pulse) const;

  /**
   * Validate detected pulse against prediction
   */
  [[nodiscard]] bool validatePulse(const VideoSyncPulse& pulse, EstimatedPulseType predicted) const;

  /**
   * Check if timing distance is valid for current state
   */
  [[nodiscard]] bool validateTiming(const VideoSyncPulse& pulse) const;

  /**
   * Synthesize missing pulse when expected pulse is not found
   */
  [[nodiscard]] VideoSyncPulse synthesizeMissingPulse(EstimatedPulseType expectedType) const;

  /**
   * Update prediction state based on processed pulse
   */
  void updatePredictionState(const VideoSyncPulse& pulse, EstimatedPulseType pulseType);

  /**
   * Advance to next frame state
   */
  void advanceFrameState();

  /**
   * Send callback event
   */
  void sendEvent(const EventData& eventData);

  /**
   * Handle vertical sync detection for synchronization
   */
  bool handleVerticalSyncDetection(const VideoSyncPulse& pulse);

  /**
   * Process pulse in vertical interval (EQ + V + EQ)
   */
  bool processVerticalInterval(const VideoSyncPulse& pulse);

  /**
   * Process horizontal line pulse
   */
  bool processHorizontalLine(const VideoSyncPulse& pulse);

  /**
   * Calculate expected pulse position based on timing
   */
  [[nodiscard]] TFloat calculateExpectedPosition(EstimatedPulseType pulseType) const;

  /**
   * Check if we need to handle missing pulses
   */
  [[nodiscard]] bool shouldSynthesizeMissingPulse(const VideoSyncPulse& nextPulse) const;
};

} // namespace IQVideoProcessor
