#include "./video_sync_orchestrator.h"

#include <algorithm>
#include <cstdio>
#include <iostream>

namespace IQVideoProcessor::Pipeline {

VideoSyncOrchestrator::VideoSyncOrchestrator(const SampleRateType sampleRate)
  : sampleRate_(sampleRate),
    rangeEstimator_(sampleRate) {}

void VideoSyncOrchestrator::setWheelProfile(const WheelProfile profile) {
  profile_ = profile;
}

void VideoSyncOrchestrator::initialize(const VideoStandardDetector::Result& detectedStandard,
                                       const EventCallback& cb) {
  callback_ = cb;
  detectedStandard_ = detectedStandard;

  // Validate input parameters
  if (detectedStandard_.standard == VideoStandard::STANDARD_UNKNOWN) {
    std::cerr << "VideoSyncOrchestrator: Initialization failed - Unknown video standard" << std::endl;
    inited_ = false;
    return;
  }

  if (detectedStandard_.horizontalLineDuration <= 0) {
    std::cerr << "VideoSyncOrchestrator: Initialization failed - Invalid horizontal line duration: "
              << detectedStandard_.horizontalLineDuration << std::endl;
    inited_ = false;
    return;
  }

  horizontalLineDuration_ = static_cast<double>(detectedStandard_.horizontalLineDuration);
  halfHorizontalLineDuration_ = 0.5 * horizontalLineDuration_;
  computeTolerance();

  // Build wheel (2-frame cycle) either from preset profile or from detected standard
  buildWheel();

  if (wheel_.empty()) {
    std::cerr << "VideoSyncOrchestrator: Initialization failed - Could not build timing wheel" << std::endl;
    inited_ = false;
    return;
  }

  clearRuntime();
  inited_ = true;

  std::cout << "VideoSyncOrchestrator: Successfully initialized for "
            << (detectedStandard_.standard == VideoStandard::PAL ? "PAL" :
                detectedStandard_.standard == VideoStandard::NTSC ? "NTSC" : "Unknown")
            << " standard with " << wheel_.size() << " wheel steps" << std::endl;
}

[[nodiscard]] bool VideoSyncOrchestrator::initialized() const { return inited_; }

void VideoSyncOrchestrator::reset() {
  inited_ = false;
  wheel_.clear();
  clearRuntime();
}

void VideoSyncOrchestrator::clearRuntime() {
  // Initialize wheel index safely - will be corrected when wheel is populated
  if (wheel_.empty()) {
    wheelIdx_ = 0;
  } else {
    wheelIdx_ = wheel_.size() - 1; // so next step is wheel[0]
  }

  locked_ = false;
  frameActive_ = false;
  lineInFrame_ = 0;
  eqPulseCounter_ = 0;
  currentCenter_ = 0.0;
  totalPhaseCorrectionCount_ = 0;
  totalProcessedSteps_ = 0;
  lastFrameResetPosition_ = 0.0;
}

void VideoSyncOrchestrator::computeTolerance() {
  // ±3 microseconds in samples, clamped to [2 samples, 0.15*H]
  const double base = static_cast<double>(sampleRate_) * 3e-6;
  double tolerance = base;
  if (tolerance < 2.0) tolerance = 2.0;
  const double maxTolerance = 0.15 * horizontalLineDuration_;
  if (tolerance > maxTolerance) tolerance = maxTolerance;
  toleranceSamples_ = tolerance;
}

void VideoSyncOrchestrator::buildWheel() {
  wheel_.clear();

  // Canonical wheels for presets. AUTO_FROM_DS uses detector counts if available; if any zero,
  // fall back to canonical values per standard.
  switch (profile_) {
    case WheelProfile::NTSC_525_60:
      buildWheelPresetNTSC();
      return;
    case WheelProfile::PAL_625_50_INTERLACED:
      buildWheelPresetPALInterlaced();
      return;
    case WheelProfile::PAL_625_50:
      buildWheelPresetPAL();
      return;
    case WheelProfile::AUTO_FROM_DS:
    default: {
      // Use detected standard counts if present; otherwise use canonical per standard
      uint32_t eqPre = detectedStandard_.eqPreCount;
      uint32_t eqPost = detectedStandard_.eqPostCount;
      uint32_t vCnt = detectedStandard_.verticalPulseCount;
      uint32_t linesPerFrame = detectedStandard_.linesPerFrame ? detectedStandard_.linesPerFrame
                   : (detectedStandard_.standard == VideoStandard::PAL ? 625u
                      : (detectedStandard_.standard == VideoStandard::NTSC ? 525u : 0u));

      if (linesPerFrame == 0) {
        // Fallback to PAL as a safe default
        linesPerFrame = 625;
      }

      if (eqPre == 0 || eqPost == 0 || vCnt == 0) {
        if (detectedStandard_.standard == VideoStandard::PAL) {
          // PAL interlaced canonical
          eqPre = 5; eqPost = 5; vCnt = 5;
        } else if (detectedStandard_.standard == VideoStandard::NTSC) {
          // NTSC interlaced canonical
          eqPre = 6; eqPost = 6; vCnt = 6;
        } else {
          // Default to PAL-like
          eqPre = 5; eqPost = 5; vCnt = 5;
        }
      }

      buildWheelFromDS(linesPerFrame, eqPre, eqPost, vCnt);
      return;
    }
  }
}

void VideoSyncOrchestrator::buildWheelFromDS(uint32_t linesPerFrame,
                                             uint32_t eqPre,
                                             uint32_t eqPost,
                                             uint32_t vCount) {
  wheel_.clear();

  const double horizontalDuration = horizontalLineDuration_;
  const double halfHorizontalDuration = halfHorizontalLineDuration_;
  const uint32_t totalLinesPerFrame = linesPerFrame;

  // Two-frame combined horizontal step count:
  // Hcount2 = 2*L - (eqPre + eqPost + vCount)
  // Derivation: per frame: Hcount*H + (eqPre+eqPost+vCount)*(H/2) = L*H
  // Over 2 frames, makes Hcount2 integral.
  const int64_t totalHorizontalSteps = static_cast<int64_t>(2 * totalLinesPerFrame) - static_cast<int64_t>(eqPre + eqPost + vCount);
  const int64_t frameAHorizontalSteps = totalHorizontalSteps / 2;         // first frame H steps
  const int64_t frameBHorizontalSteps = totalHorizontalSteps - frameAHorizontalSteps;        // second frame H steps

  auto pushSteps = [&](StepType stepType, int64_t count, double duration) {
    for (int64_t i = 0; i < count; ++i) {
      wheel_.push_back({stepType, duration});
    }
  };

  // Two successive frames, starting at vertical entry:
  // Frame A
  pushSteps(StepType::V,  vCount, halfHorizontalDuration);
  pushSteps(StepType::EQ, eqPost, halfHorizontalDuration);
  pushSteps(StepType::H,  frameAHorizontalSteps, horizontalDuration);
  pushSteps(StepType::EQ, eqPre,  halfHorizontalDuration);
  // Frame B
  pushSteps(StepType::V,  vCount, halfHorizontalDuration);
  pushSteps(StepType::EQ, eqPost, halfHorizontalDuration);
  pushSteps(StepType::H,  frameBHorizontalSteps, horizontalDuration);
  pushSteps(StepType::EQ, eqPre,  halfHorizontalDuration);

  // wheel_[0] is the first V pulse center by construction.
  // wheelIdx_ will be set so next step is wheel_[0].
  if (!wheel_.empty()) {
    wheelIdx_ = wheel_.size() - 1;
  }
}

void VideoSyncOrchestrator::buildWheelPresetNTSC() {
  // NTSC 525/60 interlaced canonical: eqPre=6, eqPost=6, vCount=6
  const uint32_t linesPerFrame = (detectedStandard_.linesPerFrame ? detectedStandard_.linesPerFrame : 525u);
  buildWheelFromDS(linesPerFrame, 6, 6, 6);
}

void VideoSyncOrchestrator::buildWheelPresetPAL() {
  // PAL 625/50 (interlaced canonical): eqPre=5, eqPost=5, vCount=5
  const uint32_t linesPerFrame = (detectedStandard_.linesPerFrame ? detectedStandard_.linesPerFrame : 625u);
  buildWheelFromDS(linesPerFrame, 5, 5, 5);
}

void VideoSyncOrchestrator::buildWheelPresetPALInterlaced() {
  // Same as PAL canonical; provided for explicit naming
  buildWheelPresetPAL();
}

inline VideoSyncOrchestrator::VideoSyncPulseType
VideoSyncOrchestrator::estimatePulseType(const VideoSyncPulse& pulse) const {
  const auto estimatedType = rangeEstimator_.estimatePulseByWidth(pulse.width);

  // Add debug output for vertical pulse detection to help diagnose misclassification
  if (estimatedType == EstimatedPulseType::VERTICAL_PULSE ||
      estimatedType == EstimatedPulseType::VERTICAL_LONG_PULSE) {
    static int verticalPulseCount = 0;
    if (++verticalPulseCount <= 10) { // Only log first 10 to avoid spam
      std::cout << "VideoSyncOrchestrator: Detected vertical pulse #" << verticalPulseCount
                << " at position " << pulse.absCenterPosition
                << " with width " << pulse.width << " samples" << std::endl;
    }
  }

  switch (estimatedType) {
    case EstimatedPulseType::HORIZONTAL_OR_EQUALIZING_PULSE:
      return VideoSyncOrchestrator::VideoSyncPulseType::HORIZONTAL_OR_EQUALIZING_PULSE;
    case EstimatedPulseType::VERTICAL_PULSE:
      return VideoSyncPulseType::VERTICAL_PULSE;
    case EstimatedPulseType::VERTICAL_LONG_PULSE:
      return VideoSyncPulseType::VERTICAL_LONG_PULSE;
    case EstimatedPulseType::UNKNOWN_PULSE:
    default:
      return VideoSyncPulseType::UNKNOWN_PULSE;
  }
}

bool VideoSyncOrchestrator::findMatchingPulse(const std::vector<VideoSyncPulse>& pulses,
                                              double expectedPosition,
                                              StepType expectedType,
                                              double& actualPosition) const {
  double bestDistance = toleranceSamples_ + 1.0; // Start with distance greater than tolerance
  bool found = false;

  for (const auto& pulse : pulses) {
    const double distance = std::fabs(pulse.absCenterPosition - expectedPosition);

    // Skip if outside tolerance window
    if (distance > toleranceSamples_) continue;

    // Check if pulse type matches expected wheel step type
    const auto pulseType = estimatePulseType(pulse);
    bool typeMatches = false;

    switch (expectedType) {
      case StepType::H:
        typeMatches = (pulseType == VideoSyncPulseType::HORIZONTAL_SYNC_PULSE ||
                      pulseType == VideoSyncPulseType::HORIZONTAL_OR_EQUALIZING_PULSE);
        break;
      case StepType::EQ:
        typeMatches = (pulseType == VideoSyncPulseType::EQUALIZING_PULSE ||
                      pulseType == VideoSyncPulseType::HORIZONTAL_OR_EQUALIZING_PULSE);
        break;
      case StepType::V:
        typeMatches = (pulseType == VideoSyncPulseType::VERTICAL_PULSE ||
                      pulseType == VideoSyncPulseType::VERTICAL_LONG_PULSE);
        break;
    }

    // Use closest matching pulse within tolerance
    if (typeMatches && distance < bestDistance) {
      bestDistance = distance;
      actualPosition = pulse.absCenterPosition;
      found = true;
    }
  }

  return found;
}

void VideoSyncOrchestrator::checkForFrameResynchronization(const std::vector<VideoSyncPulse>& pulses) {
  // Look for vertical pulses that could indicate frame timing issues
  // Only consider pulses that are clearly vertical (not misclassified horizontal pulses)

  for (const auto& pulse : pulses) {
    const auto pulseType = estimatePulseType(pulse);

    // Only process actual vertical pulses
    if (pulseType == VideoSyncPulseType::VERTICAL_PULSE ||
        pulseType == VideoSyncPulseType::VERTICAL_LONG_PULSE) {

      // Calculate where we expect the next vertical pulse based on frame timing
      // For PAL: ~625 * horizontalLineDuration samples per frame
      // For NTSC: ~525 * horizontalLineDuration samples per frame
      const double expectedFrameDuration = detectedStandard_.linesPerFrame * horizontalLineDuration_;

      // Find the closest expected vertical position
      double expectedVerticalPosition = currentCenter_;
      while (expectedVerticalPosition < pulse.absCenterPosition - expectedFrameDuration/2) {
        expectedVerticalPosition += expectedFrameDuration;
      }
      while (expectedVerticalPosition > pulse.absCenterPosition + expectedFrameDuration/2) {
        expectedVerticalPosition -= expectedFrameDuration;
      }

      const double frameTimingError = std::fabs(pulse.absCenterPosition - expectedVerticalPosition);

      // Only reset for truly significant frame timing errors (more than 1/4 frame)
      // AND prevent duplicate resets at the same position
      if (frameTimingError > expectedFrameDuration * 0.25 &&
          std::fabs(pulse.absCenterPosition - lastFrameResetPosition_) > horizontalLineDuration_) {

        // This is a major frame timing error - reset synchronization
        wheelIdx_ = 0; // Reset to start of vertical sync sequence
        currentCenter_ = pulse.absCenterPosition;
        frameActive_ = false; // Will be set correctly as wheel advances
        lineInFrame_ = 0;
        lastFrameResetPosition_ = pulse.absCenterPosition;

        std::cout << "VideoSyncOrchestrator: Frame resynchronization at position "
                  << pulse.absCenterPosition << " (frame timing error: "
                  << frameTimingError << " samples)" << std::endl;

        // Only reset once per segment
        return;
      }
    }
  }
}

bool VideoSyncOrchestrator::tryInitialLockOnVertical(const std::vector<VideoSyncPulse>& pulses) {
  // Find the first pulse in this segment that is Vertical or Vertical Long by width.
  for (const auto& pulse : pulses) {
    const auto pulseType = estimatePulseType(pulse);
    if (pulseType == VideoSyncPulseType::VERTICAL_PULSE || pulseType == VideoSyncPulseType::VERTICAL_LONG_PULSE) {
      // Anchor the timeline to this vertical center
      // Position wheel so that we're at the start of the vertical sync sequence
      currentCenter_ = pulse.absCenterPosition;
      wheelIdx_ = 0; // Start at beginning of wheel (first vertical pulse)
      locked_ = true;

      std::cout << "VideoSyncOrchestrator: ✓ Initial vertical lock acquired at position "
                << pulse.absCenterPosition << ", wheel positioned at start" << std::endl;
      return true;
    }
  }

  // No vertical pulse found - this is normal for most segments
  // Only log occasionally to avoid spam
  static int noVerticalCount = 0;
  if (++noVerticalCount % 50 == 1) { // Log every 50th attempt
    std::cout << "VideoSyncOrchestrator: Waiting for initial vertical sync pulse... (attempt "
              << noVerticalCount << ", " << pulses.size() << " pulses in segment)" << std::endl;
  }
  return false;
}

void VideoSyncOrchestrator::process(const std::vector<VideoSyncPulse>& pulses,
                                    const double processingEndPosition) {
  if (!inited_) {
    std::cerr << "VideoSyncOrchestrator: Cannot process - not initialized" << std::endl;
    return;
  }

  if (wheel_.empty()) {
    std::cerr << "VideoSyncOrchestrator: Cannot process - timing wheel is empty" << std::endl;
    return;
  }

  // FIRST-TIME INITIALIZATION: Wait for initial vertical pulse to establish timing reference
  if (!locked_) {
    if (!tryInitialLockOnVertical(pulses)) {
      // No vertical pulse found - this is normal for most segments
      // Continue waiting for the first vertical pulse to establish timing
      return;
    }
    // Successfully locked - currentCenter_ is now set to the vertical pulse position
    // The wheel is positioned so that the next step will be the first step after vertical
  }

  // FRAME SYNCHRONIZATION CHECK: Check for vertical pulses ONCE per segment (not per wheel step)
  // Only check if we have a significant number of pulses to avoid false positives
  if (pulses.size() > 5) {
    checkForFrameResynchronization(pulses);
  }

  // CONTINUOUS WHEEL SPINNING: Process from current position to processingEndPosition
  // The wheel spins continuously regardless of whether pulses are detected
  while (true) {
    const size_t nextIdx = (wheelIdx_ + 1) % wheel_.size();
    const auto& nextStep = wheel_[nextIdx];
    const auto& currentStep = wheel_[wheelIdx_];

    // Calculate expected position for the next wheel step
    const double expectedNextCenter = currentCenter_ + nextStep.distance;

    // Stop if we would exceed the current segment boundary
    if (expectedNextCenter > processingEndPosition) {
      break; // Continue in next segment from current position
    }

    // FRAME BOUNDARY DETECTION: Based on wheel step transitions
    const bool enteringVertical = (currentStep.type != StepType::V && nextStep.type == StepType::V);
    const bool leavingVertical = (currentStep.type == StepType::V && nextStep.type != StepType::V);

    // Handle frame end (entering vertical sync region)
    if (enteringVertical && frameActive_) {
      sendFrameEnd();
      frameActive_ = false;
    }

    // Handle frame begin (leaving vertical sync region)
    if (leavingVertical) {
      sendFrameBegin();
      frameActive_ = true;
      lineInFrame_ = 0;
      eqPulseCounter_ = 0;
    }

    // OPPORTUNISTIC PHASE CORRECTION: Look for matching pulses near expected position
    double actualCenter = expectedNextCenter; // Default to theoretical position
    bool phaseCorrectionApplied = findMatchingPulse(pulses, expectedNextCenter, nextStep.type, actualCenter);

    // EVENT GENERATION: Based on wheel position, not detected pulses
    const double fromCenter = currentCenter_;
    const double toCenter = actualCenter;

    if (nextStep.type == StepType::H && frameActive_) {
      sendLineDetected(fromCenter, toCenter);
    } else if (nextStep.type == StepType::EQ) {
      sendEqPulse(fromCenter, toCenter);
      ++eqPulseCounter_;
    }
    // Vertical steps: no individual pulse events (handled by frame begin/end)

    // ADVANCE WHEEL: Move to next position
    currentCenter_ = actualCenter;
    wheelIdx_ = nextIdx;
    totalProcessedSteps_++;

    // Track phase correction statistics
    if (phaseCorrectionApplied) {
      totalPhaseCorrectionCount_++;
      const double error = actualCenter - expectedNextCenter;

      // Log significant phase corrections
      if (std::fabs(error) > toleranceSamples_ * 0.5) {
        std::cout << "VideoSyncOrchestrator: Large phase correction applied, error: "
                  << error << " samples (step " << totalProcessedSteps_ << ")" << std::endl;
      }
    }
  }

  // Periodic statistics output
  if (totalProcessedSteps_ > 0 && totalProcessedSteps_ % 1000 == 0) {
    const double correctionRate = (double)totalPhaseCorrectionCount_ / totalProcessedSteps_ * 100.0;
    std::cout << "VideoSyncOrchestrator: Processed " << totalProcessedSteps_
              << " steps, phase correction rate: " << correctionRate << "%" << std::endl;
  }
}

// ---- Events ----

void VideoSyncOrchestrator::sendEvent(const EventData& e) {
  if (callback_) callback_(e);
}

void VideoSyncOrchestrator::sendFrameBegin() {
  EventData event;
  event.type = FRAME_BEGIN;
  event.frameWidth = 720;
  event.frameHeight = detectedStandard_.linesPerFrame;
  sendEvent(event);
}

void VideoSyncOrchestrator::sendFrameEnd() {
  EventData event;
  event.type = FRAME_END;
  sendEvent(event);
}

void VideoSyncOrchestrator::sendLineDetected(const double fromCenter, const double toCenter) {
  EventData event;
  event.type = LINE_DETECTED;
  event.lineNumber = lineInFrame_++;
  event.fromPosition = static_cast<TFloat>(fromCenter);
  event.sampleCount = static_cast<size_t>(std::max(0.0, toCenter - fromCenter));
  event.frameWidth = 720;
  event.frameHeight = detectedStandard_.linesPerFrame;
  sendEvent(event);
}

void VideoSyncOrchestrator::sendEqPulse(const double fromCenter, const double toCenter) {
  EventData event;
  event.type = EQUALIZATION_PULSE;
  event.pulseNumber = eqPulseCounter_;
  event.fromPosition = static_cast<TFloat>(fromCenter);
  event.sampleCount = static_cast<size_t>(std::max(0.0, toCenter - fromCenter));
  sendEvent(event);
}

} // namespace IQVideoProcessor::Pipeline
