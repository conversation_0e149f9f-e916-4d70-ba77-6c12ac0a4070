#include "./video_sync_orchestrator.h"

#include <algorithm>
#include <cstdio>

namespace IQVideoProcessor::Pipeline {

VideoSyncOrchestrator::VideoSyncOrchestrator(const SampleRateType sampleRate)
  : sampleRate_(sampleRate),
    rangeEstimator_(sampleRate) {}

void VideoSyncOrchestrator::setWheelProfile(const WheelProfile profile) {
  profile_ = profile;
}

void VideoSyncOrchestrator::initialize(const VideoStandardDetector::Result& detectedStandard,
                                       const EventCallback& cb) {
  callback_ = cb;
  ds_ = detectedStandard;

  // Trust ds_.horizontalLineDuration; require standard known
  if (ds_.standard == VideoStandard::STANDARD_UNKNOWN || ds_.horizontalLineDuration <= 0) {
    inited_ = false;
    return;
  }

  H_ = static_cast<double>(ds_.horizontalLineDuration);
  H2_ = 0.5 * H_;
  computeTolerance();

  // Build wheel (2-frame cycle) either from preset profile or from ds
  buildWheel();

  clearRuntime();
  inited_ = true;
}

[[nodiscard]] bool VideoSyncOrchestrator::initialized() const { return inited_; }

void VideoSyncOrchestrator::reset() {
  inited_ = false;
  wheel_.clear();
  clearRuntime();
}

void VideoSyncOrchestrator::clearRuntime() {
  wheelIdx_ = (wheel_.empty() ? 0 : wheel_.size() - 1); // so next step is wheel[0]
  locked_ = false;
  frameActive_ = false;
  lineInFrame_ = 0;
  eqPulseCounter_ = 0;
  currentCenter_ = 0.0;
}

void VideoSyncOrchestrator::computeTolerance() {
  // ±3 microseconds in samples, clamped to [2 samples, 0.15*H]
  const double base = static_cast<double>(sampleRate_) * 3e-6;
  double t = base;
  if (t < 2.0) t = 2.0;
  const double maxTol = 0.15 * H_;
  if (t > maxTol) t = maxTol;
  tolSamples_ = t;
}

void VideoSyncOrchestrator::buildWheel() {
  wheel_.clear();

  // Canonical wheels for presets. AUTO_FROM_DS uses detector counts if available; if any zero,
  // fall back to canonical values per standard.
  switch (profile_) {
    case WheelProfile::NTSC_525_60:
      buildWheelPresetNTSC();
      return;
    case WheelProfile::PAL_625_50_INTERLACED:
      buildWheelPresetPALInterlaced();
      return;
    case WheelProfile::PAL_625_50:
      buildWheelPresetPAL();
      return;
    case WheelProfile::AUTO_FROM_DS:
    default: {
      // Use ds counts if present; otherwise use canonical per standard
      uint32_t eqPre = ds_.eqPreCount;
      uint32_t eqPost = ds_.eqPostCount;
      uint32_t vCnt = ds_.verticalPulseCount;
      uint32_t L = ds_.linesPerFrame ? ds_.linesPerFrame
                   : (ds_.standard == VideoStandard::PAL ? 625u
                      : (ds_.standard == VideoStandard::NTSC ? 525u : 0u));

      if (L == 0) {
        // Fallback to PAL as a safe default
        L = 625;
      }

      if (eqPre == 0 || eqPost == 0 || vCnt == 0) {
        if (ds_.standard == VideoStandard::PAL) {
          // PAL interlaced canonical
          eqPre = 5; eqPost = 5; vCnt = 5;
        } else if (ds_.standard == VideoStandard::NTSC) {
          // NTSC interlaced canonical
          eqPre = 6; eqPost = 6; vCnt = 6;
        } else {
          // Default to PAL-like
          eqPre = 5; eqPost = 5; vCnt = 5;
        }
      }

      buildWheelFromDS(L, eqPre, eqPost, vCnt);
      return;
    }
  }
}

void VideoSyncOrchestrator::buildWheelFromDS(uint32_t linesPerFrame,
                                             uint32_t eqPre,
                                             uint32_t eqPost,
                                             uint32_t vCount) {
  wheel_.clear();

  const double H = H_;
  const double H2 = H2_;
  const uint32_t L = linesPerFrame;

  // Two-frame combined horizontal step count:
  // Hcount2 = 2*L - (eqPre + eqPost + vCount)
  // Derivation: per frame: Hcount*H + (eqPre+eqPost+vCount)*(H/2) = L*H
  // Over 2 frames, makes Hcount2 integral.
  const int64_t Hcount2 = static_cast<int64_t>(2 * L) - static_cast<int64_t>(eqPre + eqPost + vCount);
  const int64_t HA = Hcount2 / 2;         // first frame H steps
  const int64_t HB = Hcount2 - HA;        // second frame H steps

  auto pushN = [&](StepType t, int64_t n, double d) {
    for (int64_t i = 0; i < n; ++i) {
      wheel_.push_back({t, d});
    }
  };

  // Two successive frames, starting at vertical entry:
  // Frame A
  pushN(StepType::V,  vCount, H2);
  pushN(StepType::EQ, eqPost, H2);
  pushN(StepType::H,  HA,     H);
  pushN(StepType::EQ, eqPre,  H2);
  // Frame B
  pushN(StepType::V,  vCount, H2);
  pushN(StepType::EQ, eqPost, H2);
  pushN(StepType::H,  HB,     H);
  pushN(StepType::EQ, eqPre,  H2);

  // wheel_[0] is the first V pulse center by construction.
  // wheelIdx_ will be set so next step is wheel_[0].
  wheelIdx_ = (wheel_.empty() ? 0 : wheel_.size() - 1);
}

void VideoSyncOrchestrator::buildWheelPresetNTSC() {
  // NTSC 525/60 interlaced canonical: eqPre=6, eqPost=6, vCount=6
  const uint32_t L = (ds_.linesPerFrame ? ds_.linesPerFrame : 525u);
  buildWheelFromDS(L, 6, 6, 6);
}

void VideoSyncOrchestrator::buildWheelPresetPAL() {
  // PAL 625/50 (interlaced canonical): eqPre=5, eqPost=5, vCount=5
  const uint32_t L = (ds_.linesPerFrame ? ds_.linesPerFrame : 625u);
  buildWheelFromDS(L, 5, 5, 5);
}

void VideoSyncOrchestrator::buildWheelPresetPALInterlaced() {
  // Same as PAL canonical; provided for explicit naming
  buildWheelPresetPAL();
}

inline VideoSyncOrchestrator::VideoSyncPulseType
VideoSyncOrchestrator::estimatePulseType(const VideoSyncPulse& p) const {
  switch (rangeEstimator_.estimatePulseByWidth(p.width)) {
    case EstimatedPulseType::HORIZONTAL_OR_EQUALIZING_PULSE: return VideoSyncOrchestrator::VideoSyncPulseType::HORIZONTAL_OR_EQUALIZING_PULSE;
    case EstimatedPulseType::VERTICAL_PULSE:                  return VideoSyncPulseType::VERTICAL_PULSE;
    case EstimatedPulseType::VERTICAL_LONG_PULSE:             return VideoSyncPulseType::VERTICAL_LONG_PULSE;
    case EstimatedPulseType::UNKNOWN_PULSE:
    default:                                                  return VideoSyncPulseType::UNKNOWN_PULSE;
  }
}

bool VideoSyncOrchestrator::tryInitialLockOnVertical(const std::vector<VideoSyncPulse>& pulses) {
  // Find the first pulse in this segment that is Vertical or Vertical Long by width.
  for (const auto& p : pulses) {
    const auto pt = estimatePulseType(p);
    if (pt == VideoSyncPulseType::VERTICAL_PULSE || pt == VideoSyncPulseType::VERTICAL_LONG_PULSE) {
      // Anchor the timeline to this vertical center; next wheel step will be wheel_[0] (also V).
      currentCenter_ = p.absCenterPosition;
      locked_ = true;
      // On first lock, we do not emit any event immediately; events will be emitted while stepping.
      return true;
    }
  }
  return false;
}

void VideoSyncOrchestrator::process(const std::vector<VideoSyncPulse>& pulses,
                                    const double processingEndPosition) {
  if (!inited_) return;
  if (wheel_.empty()) return;

  // First-time lock: seek vertical pulse
  if (!locked_) {
    if (!tryInitialLockOnVertical(pulses)) {
      // No V in this segment; cannot proceed deterministically. Wait for a later segment.
      return;
    }
  }

  // Step the wheel until we cannot form a complete center-to-center segment within processingEndPosition
  while (true) {
    const size_t nextIdx = (wheelIdx_ + 1) % wheel_.size();
    const auto& nextStep = wheel_[nextIdx];
    const auto& prevStep = wheel_[wheelIdx_];

    const double expectedNextCenter = currentCenter_ + nextStep.distance;
    if (expectedNextCenter > processingEndPosition) {
      break; // stop this call; continue in next segment
    }

    // Frame boundary events are determined solely by step type transitions in the wheel.
    const bool enteringV = (prevStep.type != StepType::V && nextStep.type == StepType::V);
    const bool leavingV  = (prevStep.type == StepType::V && nextStep.type != StepType::V);

    if (enteringV && frameActive_) {
      sendFrameEnd();
      frameActive_ = false;
    }
    if (leavingV) {
      sendFrameBegin();
      frameActive_ = true;
      lineInFrame_ = 0;
      eqPulseCounter_ = 0; // reset per-frame EQ counter (optional)
    }

    // Emit the step event
    const double fromCenter = currentCenter_;
    const double toCenter   = expectedNextCenter;

    if (nextStep.type == StepType::H && frameActive_) {
      sendLineDetected(fromCenter, toCenter);
    } else if (nextStep.type == StepType::EQ) {
      sendEqPulse(fromCenter, toCenter);
      ++eqPulseCounter_;
    }
    // Vertical steps: no per-pulse event

    // Phase correction: simple window-based snap using the next pulse near expectedNextCenter.
    // We don't track cursor across segments; we just find the nearest within tolerance in this vector.
    // Pulses are sorted; use lower_bound-like scan.
    auto it = std::lower_bound(
      pulses.begin(), pulses.end(), expectedNextCenter,
      [](const VideoSyncPulse& p, double center){ return p.absCenterPosition < center; });

    bool snapped = false;
    if (it != pulses.end()) {
      const double realC = it->absCenterPosition;
      if (std::fabs(realC - expectedNextCenter) <= tolSamples_) {
        // Snap: shift currentCenter_ by the error so the step lands exactly on realC
        const double err = realC - expectedNextCenter;
        currentCenter_ += err;
        snapped = true;
      }
    }
    // Also check previous element (closest on the left)
    if (!snapped && it != pulses.begin()) {
      auto itPrev = std::prev(it);
      const double realC = itPrev->absCenterPosition;
      if (std::fabs(realC - expectedNextCenter) <= tolSamples_) {
        const double err = realC - expectedNextCenter;
        currentCenter_ += err;
        snapped = true;
      }
    }

    // Advance
    currentCenter_ = expectedNextCenter; // includes phase shift if snapped
    wheelIdx_ = nextIdx;
  }
}

// ---- Events ----

void VideoSyncOrchestrator::sendEvent(const EventData& e) {
  if (callback_) callback_(e);
}

void VideoSyncOrchestrator::sendFrameBegin() {
  EventData e;
  e.type = FRAME_BEGIN;
  e.frameWidth = 720;
  e.frameHeight = ds_.linesPerFrame;
  sendEvent(e);
}

void VideoSyncOrchestrator::sendFrameEnd() {
  EventData e;
  e.type = FRAME_END;
  sendEvent(e);
}

void VideoSyncOrchestrator::sendLineDetected(const double fromCenter, const double toCenter) {
  EventData e;
  e.type = LINE_DETECTED;
  e.lineNumber = lineInFrame_++;
  e.fromPosition = static_cast<TFloat>(fromCenter);
  e.sampleCount = static_cast<size_t>(std::max(0.0, toCenter - fromCenter));
  e.frameWidth = 720;
  e.frameHeight = ds_.linesPerFrame;
  sendEvent(e);
}

void VideoSyncOrchestrator::sendEqPulse(const double fromCenter, const double toCenter) {
  EventData e;
  e.type = EQUALIZATION_PULSE;
  e.pulseNumber = eqPulseCounter_;
  e.fromPosition = static_cast<TFloat>(fromCenter);
  e.sampleCount = static_cast<size_t>(std::max(0.0, toCenter - fromCenter));
  sendEvent(e);
}

} // namespace IQVideoProcessor::Pipeline
