#include "./video_standard_detector.h"
#include "../../../video_processor_configs.h"
#include <algorithm>
#include <cmath>
#include <limits>

namespace IQVideoProcessor::Pipeline {

constexpr size_t SYNC_PULSES_PROCESS_THRESHOLD = 625 * 3; // Process after collecting 5 frames of 625 lines
constexpr size_t SYNC_PULSES_BUFFER_SIZE = SYNC_PULSES_PROCESS_THRESHOLD * 2; // Buffer up to 10 frames of 625 lines
constexpr size_t GIVE_UP_ITERATIONS_THRESHOLD = static_cast<size_t>(SYNC_PULSES_PROCESS_THRESHOLD / LINES_PER_CHUNK) * 3;

VideoStandardDetector::VideoStandardDetector(const SampleRateType sampleRate) : sampleRate_(sampleRate) {
  collectedSyncPulses_.resize(SYNC_PULSES_BUFFER_SIZE);
  discoveredHorizontalPulses_.resize(SYNC_PULSES_BUFFER_SIZE);
  discoveredVerticalPulses_.resize(SYNC_PULSES_BUFFER_SIZE);
  discoveredEqualizingPulses_.resize(SYNC_PULSES_BUFFER_SIZE);
  reset();
}

void VideoStandardDetector::reset() {
  giveUpIterations_ = 0;
  collectedSyncPulses_.clear();
  discoveredHorizontalPulses_.clear();
  discoveredVerticalPulses_.clear();
  discoveredEqualizingPulses_.clear();
  result_ = Result{};
}

[[nodiscard]] VideoStandardDetector::Result& VideoStandardDetector::processSegmentSyncPulses(const std::vector<VideoSyncPulse>& syncPulses) {
  if (result_.status != DETECTION_IN_PROGRESS) {
    return result_; // Detection already completed or failed
  }

  if (++giveUpIterations_ > GIVE_UP_ITERATIONS_THRESHOLD) {
    result_.status = DETECTION_FAILED;
    return result_;
  }

  collectedSyncPulses_.insert(collectedSyncPulses_.end(), syncPulses.begin(), syncPulses.end());
  if (collectedSyncPulses_.size() < SYNC_PULSES_PROCESS_THRESHOLD) {
    return result_; // Not enough pulses collected yet
  }

  discoverValidSyncPulses();
  analyzeDiscoveredPulses();

  return result_;
}

void VideoStandardDetector::analyzeDiscoveredPulses() {
  // Helper struct to capture runs (contiguous series) and their spacing stats
  struct Run {
    size_t start{0};
    size_t end{0};
    size_t length{0};       // pulses in run
    TFloat sumSpacing{0};   // sum of gaps (samples)
    size_t gaps{0};         // number of gaps (length - 1)
    TFloat meanSpacing{0};  // sumSpacing / gaps (if gaps > 0)
  };

  auto buildRuns = [](const std::vector<VideoSyncPulse>& v,
                      const auto& inRange) -> std::vector<Run> {
    std::vector<Run> runs;
    if (v.empty()) return runs;
    size_t n = v.size();
    size_t runStart = 0;
    TFloat sumSpacing = 0;
    size_t gaps = 0;

    for (size_t i = 0; i + 1 < n; ++i) {
      TFloat d = v[i + 1].absCenterPosition - v[i].absCenterPosition;
      bool ok = inRange(d);
      if (ok) {
        sumSpacing += d;
        ++gaps;
      } else {
        size_t runEnd = i;
        size_t len = runEnd >= runStart ? (runEnd - runStart + 1) : 1;
        if (len >= 1) {
          Run r;
          r.start = runStart;
          r.end = runEnd;
          r.length = len;
          r.sumSpacing = sumSpacing;
          r.gaps = gaps;
          r.meanSpacing = (gaps > 0) ? (sumSpacing / static_cast<TFloat>(gaps)) : 0;
          runs.push_back(r);
        }
        // Start a new run at i+1
        runStart = i + 1;
        sumSpacing = 0;
        gaps = 0;
      }
    }
    // Close last run
    size_t runEnd = n - 1;
    size_t len = runEnd >= runStart ? (runEnd - runStart + 1) : 1;
    Run r;
    r.start = runStart;
    r.end = runEnd;
    r.length = len;
    r.sumSpacing = sumSpacing;
    r.gaps = gaps;
    r.meanSpacing = (gaps > 0) ? (sumSpacing / static_cast<TFloat>(gaps)) : 0;
    runs.push_back(r);
    return runs;
  };

  auto pickLongestRun = [](const std::vector<Run>& runs) -> Run {
    Run best{};
    size_t bestLen = 0;
    for (const auto& r : runs) {
      if (r.length > bestLen) {
        best = r;
        bestLen = r.length;
      }
    }
    return best;
  };

  // 1) Build runs for H, V, EQ with appropriate distance checks
  const auto hRuns = buildRuns(discoveredHorizontalPulses_,
                               [this](const TFloat d){ return signalRangeEstimator_.isHorizontalLineDistance(d); });
  const auto vRuns = buildRuns(discoveredVerticalPulses_,
                               [this](const TFloat d){ return signalRangeEstimator_.isHalfHorizontalLineDistance(d); });
  const auto eqRuns = buildRuns(discoveredEqualizingPulses_,
                                [this](const TFloat d){ return signalRangeEstimator_.isHalfHorizontalLineDistance(d); });

  const Run bestH = pickLongestRun(hRuns);
  const Run bestV = pickLongestRun(vRuns);
  const Run bestEQ = pickLongestRun(eqRuns);

  // Export vertical pulse count from the best vertical run
  result_.verticalPulseCount = static_cast<uint32_t>(bestV.length);

  // 2) Robust estimate of horizontal line duration (samples) using all sufficiently long H runs
  //    Weight by #gaps (run length - 1) to reduce bias from short runs.
  TFloat hSum = 0;
  size_t hGaps = 0;
  for (const auto& r : hRuns) {
    if (r.gaps >= 2) { // runs with at least 3 pulses
      hSum += r.sumSpacing;
      hGaps += r.gaps;
    }
  }
  TFloat horizontalLineSamples = 0;
  if (hGaps > 0) {
    horizontalLineSamples = hSum / static_cast<TFloat>(hGaps);
  } else if (bestH.gaps > 0) {
    horizontalLineSamples = bestH.meanSpacing;
  }

  result_.horizontalLineDuration = horizontalLineSamples;
  result_.horizontalFrequencyHz = (horizontalLineSamples > 0)
      ? (static_cast<TFloat>(sampleRate_) / horizontalLineSamples)
      : 0;

  // 3) Compute absolute min/max widths for H and EQ in seconds (across all discovered pulses)
  auto computeWidthMinMaxSec = [this](const std::vector<VideoSyncPulse>& pulses,
                                      TFloat& minSec, TFloat& maxSec) {
    if (pulses.empty()) {
      minSec = 0;
      maxSec = 0;
      return;
    }
    TFloat minW = std::numeric_limits<TFloat>::infinity();
    TFloat maxW = static_cast<TFloat>(0);
    for (const auto& p : pulses) {
      if (p.width < minW) minW = p.width;
      if (p.width > maxW) maxW = p.width;
    }
    if (!std::isfinite(minW)) minW = 0;
    minSec = minW / static_cast<TFloat>(sampleRate_);
    maxSec = maxW / static_cast<TFloat>(sampleRate_);
  };
  computeWidthMinMaxSec(discoveredHorizontalPulses_, result_.hSyncWidthMinSec, result_.hSyncWidthMaxSec);
  computeWidthMinMaxSec(discoveredEqualizingPulses_, result_.eqPulseWidthMinSec, result_.eqPulseWidthMaxSec);

  // 4) Vertical clusters (one cluster per field) to estimate field rate directly
  //    Cluster by half-line spacing adjacency among vertical pulses.
  std::vector<size_t> vClusterStartIdx;
  std::vector<TFloat> vClusterStartTime;
  if (!discoveredVerticalPulses_.empty()) {
    vClusterStartIdx.push_back(0);
    vClusterStartTime.push_back(discoveredVerticalPulses_[0].absCenterPosition);
    for (size_t i = 1; i < discoveredVerticalPulses_.size(); ++i) {
      TFloat d = discoveredVerticalPulses_[i].absCenterPosition - discoveredVerticalPulses_[i - 1].absCenterPosition;
      if (!signalRangeEstimator_.isHalfHorizontalLineDistance(d)) {
        vClusterStartIdx.push_back(i);
        vClusterStartTime.push_back(discoveredVerticalPulses_[i].absCenterPosition);
      }
    }
  }

  // Field period from cluster start times
  TFloat fieldPeriodSamples = 0;
  if (vClusterStartTime.size() >= 2) {
    TFloat sum = 0;
    size_t cnt = 0;
    for (size_t i = 1; i < vClusterStartTime.size(); ++i) {
      TFloat dt = vClusterStartTime[i] - vClusterStartTime[i - 1];
      if (dt > 0) { sum += dt; ++cnt; }
    }
    if (cnt > 0) {
      fieldPeriodSamples = sum / static_cast<TFloat>(cnt);
    }
  }

  TFloat fieldRateMeasuredHz = 0;
  if (fieldPeriodSamples > 0) {
    fieldRateMeasuredHz = static_cast<TFloat>(sampleRate_) / fieldPeriodSamples;
  }

  // 5) Determine pre- and post- equalizing series around the longest vertical run (if present)
  size_t preEqCount = 0;
  size_t postEqCount = 0;
  if (bestV.length >= 1 && !discoveredVerticalPulses_.empty() && !discoveredEqualizingPulses_.empty()) {
    const auto& vFirst = discoveredVerticalPulses_[bestV.start];
    const auto& vLast  = discoveredVerticalPulses_[bestV.end];

    // find last EQ before vFirst
    int preIdx = -1;
    for (int i = static_cast<int>(discoveredEqualizingPulses_.size()) - 1; i >= 0; --i) {
      if (discoveredEqualizingPulses_[static_cast<size_t>(i)].absCenterPosition < vFirst.absCenterPosition) {
        preIdx = i;
        break;
      }
    }
    if (preIdx >= 0) {
      size_t s = static_cast<size_t>(preIdx);
      size_t start = s;
      while (start > 0) {
        TFloat d = discoveredEqualizingPulses_[start].absCenterPosition - discoveredEqualizingPulses_[start - 1].absCenterPosition;
        if (!signalRangeEstimator_.isHalfHorizontalLineDistance(d)) break;
        --start;
      }
      preEqCount = s - start + 1;
    }

    // find first EQ after vLast
    size_t postStart = discoveredEqualizingPulses_.size();
    for (size_t i = 0; i < discoveredEqualizingPulses_.size(); ++i) {
      if (discoveredEqualizingPulses_[i].absCenterPosition > vLast.absCenterPosition) {
        postStart = i;
        break;
      }
    }
    if (postStart < discoveredEqualizingPulses_.size()) {
      size_t end = postStart;
      while (end + 1 < discoveredEqualizingPulses_.size()) {
        TFloat d = discoveredEqualizingPulses_[end + 1].absCenterPosition - discoveredEqualizingPulses_[end].absCenterPosition;
        if (!signalRangeEstimator_.isHalfHorizontalLineDistance(d)) break;
        ++end;
      }
      postEqCount = end - postStart + 1;
    }
  }
  result_.eqPreCount = static_cast<uint32_t>(preEqCount);
  result_.eqPostCount = static_cast<uint32_t>(postEqCount);

  // 6) Estimate lines per field/frame and decide standard
  // Nominals
  constexpr TFloat Fh_NTSC = static_cast<TFloat>(15734.264); // approx
  constexpr TFloat Fh_PAL  = static_cast<TFloat>(15625.000);
  constexpr TFloat tolFrac = static_cast<TFloat>(0.01);      // 1% tolerance for frequency proximity

  // From vertical cluster period and measured H period, derive lines/field if possible
  TFloat linesPerFieldMeasured = 0;
  if (fieldPeriodSamples > 0 && horizontalLineSamples > 0) {
    linesPerFieldMeasured = fieldPeriodSamples / horizontalLineSamples;
  }

  // Heuristic from vertical run length (5 for 625/50, 6 for 525/60), robust to missed pulses
  auto inferFamilyFromVRuns = [&]() -> int {
    if (vRuns.empty()) return 0;
    int count5 = 0, count6 = 0;
    for (const auto& r : vRuns) {
      if (r.length >= 5 && r.length <= 7) {
        int target = (std::abs(static_cast<int>(r.length) - 5) <= std::abs(static_cast<int>(r.length) - 6)) ? 5 : 6;
        if (target == 5) ++count5; else ++count6;
      } else if (r.length == 4) {
        ++count5; // bias 4 toward 5
      } else if (r.length >= 8) {
        ++count6;
      }
    }
    if (count5 == 0 && count6 == 0) return 0;
    return (count6 > count5) ? 6 : 5;
  };

  int vRunFamily = inferFamilyFromVRuns(); // 5 or 6 (0 = unknown)

  bool is525_60 = false;
  bool is625_50 = false;

  // Primary: linesPerFieldMeasured if reliable
  if (linesPerFieldMeasured > 0) {
    if (std::abs(linesPerFieldMeasured - static_cast<TFloat>(262.5)) <= static_cast<TFloat>(3.0)) {
      is525_60 = true;
    } else if (std::abs(linesPerFieldMeasured - static_cast<TFloat>(312.5)) <= static_cast<TFloat>(3.0)) {
      is625_50 = true;
    }
  }

  // Secondary: vertical run family
  if (!is525_60 && !is625_50 && vRunFamily != 0) {
    if (vRunFamily == 6) is525_60 = true;
    else if (vRunFamily == 5) is625_50 = true;
  }

  // Tertiary: horizontal frequency proximity if still ambiguous
  if (!is525_60 && !is625_50 && result_.horizontalFrequencyHz > 0) {
    TFloat fh = result_.horizontalFrequencyHz;
    TFloat errN = std::abs(fh - Fh_NTSC) / Fh_NTSC;
    TFloat errP = std::abs(fh - Fh_PAL) / Fh_PAL;
    if (errN < errP && errN <= tolFrac * static_cast<TFloat>(2.0)) {
      is525_60 = true;
    } else if (errP <= tolFrac * static_cast<TFloat>(2.0)) {
      is625_50 = true;
    }
  }

  // 7) Finalize fields based on family. Prefer derived field rate from H freq if measured is far off.
  auto chooseFieldRate = [&](TFloat linesPerFieldNominal) -> TFloat {
    const TFloat derived = (result_.horizontalFrequencyHz > 0)
      ? (result_.horizontalFrequencyHz / linesPerFieldNominal)
      : static_cast<TFloat>(0);
    if (fieldRateMeasuredHz <= 0 && derived > 0) return derived;
    if (fieldRateMeasuredHz > 0 && derived <= 0) return fieldRateMeasuredHz;
    if (fieldRateMeasuredHz > 0 && derived > 0) {
      const TFloat rel = std::abs(fieldRateMeasuredHz - derived) / derived;
      if (rel > static_cast<TFloat>(0.05)) {
        // If measured deviates >5%, trust derived (more stable with missed vertical clusters)
        return derived;
      } else {
        // Close enough: average them
        return static_cast<TFloat>(0.5) * (fieldRateMeasuredHz + derived);
      }
    }
    return static_cast<TFloat>(0);
  };

  if (is525_60) {
    result_.linesPerFrame = 525;
    result_.fieldRateHz = chooseFieldRate(static_cast<TFloat>(262.5));
    result_.standard = NTSC; // First-guess: NTSC family (cannot distinguish color system variants via sync only)
  } else if (is625_50) {
    result_.linesPerFrame = 625;
    result_.fieldRateHz = chooseFieldRate(static_cast<TFloat>(312.5));
    result_.standard = PAL;  // Default to PAL for 625/50 (SECAM indistinguishable from sync alone)
  } else {
    result_.standard = STANDARD_UNKNOWN;
    // Best-effort fieldRate: use measured if available, else try generic division by measured lines/field
    if (fieldRateMeasuredHz > 0) {
      result_.fieldRateHz = fieldRateMeasuredHz;
    } else if (result_.horizontalFrequencyHz > 0 && linesPerFieldMeasured > 0) {
      result_.fieldRateHz = result_.horizontalFrequencyHz / std::max(static_cast<TFloat>(1.0), linesPerFieldMeasured);
    }
  }

  // Frame rate = field rate / 2 (assuming interlaced)
  if (result_.fieldRateHz > 0) {
    result_.frameRate = result_.fieldRateHz / static_cast<TFloat>(2.0);
  }

  // Decide status:
  if (result_.horizontalFrequencyHz > 0 && (result_.fieldRateHz > 0 || result_.standard != STANDARD_UNKNOWN)) {
    result_.status = DETECTION_COMPLETE;
  } else {
    result_.status = DETECTION_FAILED;
  }
}

void VideoStandardDetector::discoverValidSyncPulses() {
  for (auto i = 1u; i < collectedSyncPulses_.size() - 1; ++i) {
    const auto& prevPulse = collectedSyncPulses_[i - 1];
    const auto& currentPulse = collectedSyncPulses_[i];
    const auto& nextPulse = collectedSyncPulses_[i + 1];

    auto const currentPulseType = detectCurrentPulseType(prevPulse, currentPulse, nextPulse);
    switch (currentPulseType) {
    case HORIZONTAL_SYNC_PULSE: discoveredHorizontalPulses_.push_back(currentPulse); break;
    case VERTICAL_SYNC_PULSE: discoveredVerticalPulses_.push_back(currentPulse); break;
    case EQUALIZING_PULSE: discoveredEqualizingPulses_.push_back(currentPulse); break;
    default: break; // Ignore unknown pulses
    }
  }
}

inline VideoStandardDetector::VideoSyncPulseType VideoStandardDetector::detectCurrentPulseType(
  const VideoSyncPulse &prevPulse,
  const VideoSyncPulse &currentPulse,
  const VideoSyncPulse &nextPulse
) const {
  auto const currentEstPulseType = signalRangeEstimator_.estimatePulseByWidth(currentPulse.width);
  auto const prevEstPulseType = signalRangeEstimator_.estimatePulseByWidth(prevPulse.width);

  auto const currentEstDistanceType = signalRangeEstimator_.estimateSignalDistance(currentPulse.absCenterPosition - prevPulse.absCenterPosition);
  auto const nextEstDistanceType = signalRangeEstimator_.estimateSignalDistance(nextPulse.absCenterPosition - currentPulse.absCenterPosition);

  // TODO: Simplify the conditions below, using nesting checking
  if (currentEstDistanceType == HORIZONTAL_DISTANCE && nextEstDistanceType == HORIZONTAL_DISTANCE) {
    // We are dialing with horizontal pulse between two other horizontal pulses, consider it horizontal
    return HORIZONTAL_SYNC_PULSE;
  }
  if (currentEstDistanceType == HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // We are dealing with the last pulse in the lines series, it will be equalizing pulse
    return EQUALIZING_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // We are dealing with the equalizing pulse between 2 other equalizing pulses, consider it equalizing
    return EQUALIZING_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == _70PERCENT_HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // We are dealing with the last equalizing pulse before vertical sync pulses, consider it equalizing
    return EQUALIZING_PULSE;
  }
  if (currentEstDistanceType == _70PERCENT_HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == VERTICAL_PULSE) {
    // We are dealing with the first vertical sync pulse after equalizing pulses, consider it vertical
    return VERTICAL_SYNC_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == VERTICAL_PULSE) {
    // We are dealing with the middle vertical sync pulse between other vertical sync pulses, consider it vertical
    return VERTICAL_SYNC_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == _33PERCENT_HORIZONTAL_DISTANCE && currentEstPulseType == VERTICAL_PULSE) {
    // We are dealing with the last vertical sync pulse before equalizing pulses, consider it vertical
    return VERTICAL_SYNC_PULSE;
  }
  if (currentEstDistanceType == _33PERCENT_HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE && prevEstPulseType == VERTICAL_PULSE) {
    // We are dealing with the first equalizing pulse after vertical sync pulses, consider it equalizing
    return EQUALIZING_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE && prevEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // We are dealing with the last equalizing pulse before horizontal sync pulses, or with the first horizontal sync pulse after equalizing pulses.
    // Differentiate by width: narrower is horizontal sync, wider is equalizing
    return signalRangeEstimator_.comparePulseByWidth(prevPulse.width, currentPulse.width) == SignalRangeEstimator::LEFT_LESS_THAN_RIGHT
      ? HORIZONTAL_SYNC_PULSE
      : EQUALIZING_PULSE;
  }

  return UNKNOWN;
}

} // namespace IQVideoProcessor