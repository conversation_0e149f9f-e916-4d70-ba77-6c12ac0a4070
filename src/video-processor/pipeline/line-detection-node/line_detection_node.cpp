#include "./line_detection_node.h"
#include "../../helpers/helpers.h"

#include "./devtools/data_exporter.hpp"

namespace IQVideoProcessor::Pipeline {

std::vector<TFloat> syncPositionsGraphic;

LineDetectionNode::LineDetectionNode(const SampleRateType sampleRate) : sampleRate_(sampleRate), segmentSyncDetectionFilter_(sampleRate) {
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  auto& filteredSegment = segmentSyncDetectionFilter_.process(segment);
  auto [videoSyncPulses, processingEndPosition] = segmentPulsesDetector_.process(filteredSegment);

  // <DEBUGGING>
  if (syncPositionsGraphic.size() != segment.totalSamples) {
    syncPositionsGraphic.resize(segment.totalSamples);
  }
  std::fill(syncPositionsGraphic.begin(), syncPositionsGraphic.end(), 0);
  for (const auto & detected_video_sync_pulse : videoSyncPulses) {
    // auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.centerPosition) + segment.effectiveOffset - segment.effectiveStartPosition;
    auto fallingPos = static_cast<uint32_t>(detected_video_sync_pulse.fallingFrontPosition) + segment.effectiveOffset;
    auto risingPos = static_cast<uint32_t>(detected_video_sync_pulse.risingFrontPosition) + segment.effectiveOffset;
    syncPositionsGraphic[fallingPos] = -35;
    syncPositionsGraphic[risingPos] = -35;
  }
  DevTools::export_debug_data<TFloat>("LDN", "fronts", segment.segmentIndex, syncPositionsGraphic.data(), syncPositionsGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);
  // </DEBUGGING>

  const auto& standardDetectionResult = videoStandardDetector_.processSegmentSyncPulses(videoSyncPulses);
  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_IN_PROGRESS) {
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_FAILED) {
    if (!standardDetectorRetryCountdown_.running()) {
      standardDetectorRetryCountdown_.reset();
    }
    // Waiting for ~1 second before retrying to detect the standard again
    if (standardDetectorRetryCountdown_.tick(filteredSegment.effectiveSamples)) {
      videoStandardDetector_.reset();
      standardDetectorRetryCountdown_.stop(); // just in case
    }
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_COMPLETE) {
    // Successfully detected the standard, no further action needed
    standardDetectorRetryCountdown_.stop(); // Stop further retries

    if (!vsStateMachine_.initialized()) {
      // vsStateMachine_.initialize(standardDetectionResult, [](const VideoSyncStateMachine::EventData& event) {
      //   // Forward events to the outside world
      //   std::cout << "VideoSyncStateMachine Event: Type=" << event.type
      //             << ", FrameWidth=" << event.frameWidth
      //             << ", FrameHeight=" << event.frameHeight
      //             << ", PulseNumber=" << event.pulseNumber
      //             << ", LineNumber=" << event.lineNumber
      //             << ", FromPosition=" << event.fromPosition
      //             << ", SampleCount=" << event.sampleCount
      //             << std::endl;
      // });
      vsOrchestrator_.initialize(standardDetectionResult, [](const VideoSyncOrchestrator::EventData &event) {
        std::cout << "VideoSyncOrchestrator Event: Type=" << event.type
                  << ", FrameWidth=" << event.frameWidth
                  << ", FrameHeight=" << event.frameHeight
                  << ", PulseNumber=" << event.pulseNumber
                  << ", LineNumber=" << event.lineNumber
                  << ", FromPosition=" << event.fromPosition
                  << ", SampleCount=" << event.sampleCount
                  // << ", FieldParity=" << event.fieldParity
                  << std::endl;
      });
    }
  }

  if (vsOrchestrator_.initialized()) {
    vsOrchestrator_.process(videoSyncPulses, processingEndPosition);
  }
  //
  // if (vsStateMachine_.initialized()) {
  //   vsStateMachine_.process(videoSyncPulses, processingEndPosition);
  // }

  return running();
}

} // namespace IQVideoProcessor::Pipeline
